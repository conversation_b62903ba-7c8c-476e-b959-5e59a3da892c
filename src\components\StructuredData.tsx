import React from 'react'
import { Helmet } from 'react-helmet-async'
import { useSimpleTranslation } from '../i18n/simple-hooks'

export interface StructuredDataProps {
  /** 页面类型 */
  type?: 'website' | 'organization' | 'product' | 'article' | 'faq'
  /** 应用名称键 */
  appKey?: string
  /** 页面标题 */
  title?: string
  /** 页面描述 */
  description?: string
  /** 页面URL */
  url?: string
  /** 图片URL */
  image?: string
  /** 文章发布日期（仅用于article类型） */
  publishedDate?: string
  /** 文章修改日期（仅用于article类型） */
  modifiedDate?: string
  /** 作者信息（仅用于article类型） */
  author?: string
  /** FAQ数据（仅用于faq类型） */
  faqData?: Array<{ question: string; answer: string }>
}

/**
 * 结构化数据组件
 * 生成JSON-LD格式的结构化数据，提升SEO效果
 */
const StructuredData: React.FC<StructuredDataProps> = ({
  type = 'website',
  appKey,
  title,
  description,
  url,
  image,
  publishedDate,
  modifiedDate,
  author,
  faqData
}) => {
  const { t } = useSimpleTranslation()

  // 生成基础的组织信息
  const organizationData = {
    "@type": "Organization",
    "name": "MedSci梅斯医学",
    "url": "https://ai.medsci.cn",
    "logo": {
      "@type": "ImageObject",
      "url": "https://img.medsci.cn/202412/8a5663cb16a84c45b9c8c5db4eda0075-6pyGtTcj0asd.png"
    },
    "sameAs": [
      "https://www.medsci.cn",
      "https://weibo.com/medsci",
      "https://mp.weixin.qq.com/s/medsci"
    ],
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": "customer service",
      "email": "<EMAIL>"
    }
  }

  // 生成网站结构化数据
  const generateWebsiteData = () => {
    const currentUrl = url || window.location.href
    const currentTitle = title || document.title
    const currentDescription = description || t('seo.descriptions.default')
    const currentImage = image || t('seo.images.default')

    return {
      "@context": "https://schema.org",
      "@type": "WebSite",
      "name": currentTitle,
      "description": currentDescription,
      "url": currentUrl,
      "image": currentImage,
      "publisher": organizationData,
      "potentialAction": {
        "@type": "SearchAction",
        "target": {
          "@type": "EntryPoint",
          "urlTemplate": "https://ai.medsci.cn/search?q={search_term_string}"
        },
        "query-input": "required name=search_term_string"
      }
    }
  }

  // 生成产品结构化数据
  const generateProductData = () => {
    if (!appKey) return null

    const appName = t(`pageTitle.apps.${appKey}`)
    const appDescription = description || t(`seo.descriptions.${appKey}`)
    const appImage = image || t(`seo.images.${appKey}`)
    const currentUrl = url || window.location.href

    return {
      "@context": "https://schema.org",
      "@type": "SoftwareApplication",
      "name": appName,
      "description": appDescription,
      "url": currentUrl,
      "image": appImage,
      "applicationCategory": "AI Assistant",
      "operatingSystem": "Web Browser",
      "offers": {
        "@type": "Offer",
        "price": "0",
        "priceCurrency": "CNY",
        "availability": "https://schema.org/InStock"
      },
      "publisher": organizationData,
      "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": "4.8",
        "reviewCount": "1000",
        "bestRating": "5",
        "worstRating": "1"
      }
    }
  }

  // 生成文章结构化数据
  const generateArticleData = () => {
    if (!title || !description) return null

    const currentUrl = url || window.location.href
    const currentImage = image || t('seo.images.default')
    const currentAuthor = author || "MedSci AI Team"
    const currentPublishedDate = publishedDate || new Date().toISOString()
    const currentModifiedDate = modifiedDate || new Date().toISOString()

    return {
      "@context": "https://schema.org",
      "@type": "Article",
      "headline": title,
      "description": description,
      "url": currentUrl,
      "image": currentImage,
      "author": {
        "@type": "Person",
        "name": currentAuthor
      },
      "publisher": organizationData,
      "datePublished": currentPublishedDate,
      "dateModified": currentModifiedDate,
      "mainEntityOfPage": {
        "@type": "WebPage",
        "@id": currentUrl
      }
    }
  }

  // 生成FAQ结构化数据
  const generateFAQData = () => {
    if (!faqData || faqData.length === 0) return null

    return {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": faqData.map(item => ({
        "@type": "Question",
        "name": item.question,
        "acceptedAnswer": {
          "@type": "Answer",
          "text": item.answer
        }
      }))
    }
  }

  // 根据类型生成对应的结构化数据
  const generateStructuredData = () => {
    switch (type) {
      case 'organization':
        return {
          "@context": "https://schema.org",
          ...organizationData
        }
      case 'product':
        return generateProductData()
      case 'article':
        return generateArticleData()
      case 'faq':
        return generateFAQData()
      case 'website':
      default:
        return generateWebsiteData()
    }
  }

  const structuredData = generateStructuredData()

  if (!structuredData) {
    return null
  }

  return (
    <Helmet>
      <script type="application/ld+json">
        {JSON.stringify(structuredData, null, 2)}
      </script>
    </Helmet>
  )
}

export default StructuredData
