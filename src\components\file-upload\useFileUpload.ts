import { useState, useCallback } from 'react'
import { message } from 'antd'
import { Dify<PERSON>pi, IFileLocal } from '../../api/src/dify-api'
import { IFileType } from '../../api/src/types'
import { getFileTypeByName } from '../../api/src/utils/file-type'
import { useSimpleTranslation } from '../../i18n/simple-hooks'
import { FileUploadStatusItem } from './FileUploadStatus'

export interface UseFileUploadOptions {
  difyApi: DifyApi
  maxFiles?: number
  onPreCheck?: () => boolean
  onUploadSuccess?: (files: File[], uploadData: IFileLocal[]) => void
  onUploadError?: (error: string) => void
}

export interface UseFileUploadReturn {
  uploadedFiles: File[]
  uploadData: IFileLocal[]
  uploadStatusFiles: FileUploadStatusItem[]
  isUploading: boolean
  handleFilesUploaded: (files: File[]) => Promise<void>
  removeFile: (index: number) => void
  removeFileByUid: (uid: string) => void
  clearFiles: () => void
}

export const useFileUpload = ({
  difyApi,
  maxFiles = 10,
  onPreCheck,
  onUploadSuccess,
  onUploadError
}: UseFileUploadOptions): UseFileUploadReturn => {
  const { t } = useSimpleTranslation()
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([])
  const [uploadData, setUploadData] = useState<IFileLocal[]>([])
  const [uploadStatusFiles, setUploadStatusFiles] = useState<FileUploadStatusItem[]>([])
  const [isUploading, setIsUploading] = useState(false)

  const handleFilesUploaded = useCallback(async (files: File[]) => {
    // 预检查（登录、支付状态等）
    if (onPreCheck && !onPreCheck()) {
      return
    }

    // 检查当前已上传文件数量 + 新文件数量是否超过限制
    const currentFileCount = uploadedFiles.length
    const availableSlots = maxFiles - currentFileCount

    if (availableSlots <= 0) {
      message.error(t('chat.maxFilesReached').replace('{count}', maxFiles.toString()))
      return
    }

    if (files.length > availableSlots) {
      message.error(t('chat.maxFilesExceeded')
        .replace('{available}', availableSlots.toString())
        .replace('{max}', maxFiles.toString()))
      return
    }

    // 限制批量选择文件数量（使用动态配置值）
    if (files.length > maxFiles) {
      message.error(t('chat.batchUploadLimit').replace('{count}', maxFiles.toString()))
      return
    }

    setIsUploading(true)

    try {
      // 使用所有文件，因为前面已经验证过数量
      const limitFiles = files

      const successFiles: File[] = []
      const successUploads: IFileLocal[] = []

      // 为每个文件创建初始状态
      const initialStatusFiles: FileUploadStatusItem[] = limitFiles.map((file, index) => {
        const detectedType = getFileTypeByName(file.name);
        const fileType: 'document' | 'image' | 'audio' | 'video' =
          (detectedType as unknown as 'document' | 'image' | 'audio' | 'video') || 'document';

        return {
          uid: `upload-${Date.now()}-${index}`,
          name: file.name,
          status: 'uploading' as const,
          size: file.size,
          type: fileType,
          percent: 0
        }
      })

      // 添加到状态文件列表
      setUploadStatusFiles(prev => [...prev, ...initialStatusFiles])

      // 逐个处理文件上传
      for (let i = 0; i < limitFiles.length; i++) {
        const file = limitFiles[i]
        const statusFile = initialStatusFiles[i]

        try {
          // 模拟上传进度
          const progressInterval = setInterval(() => {
            setUploadStatusFiles(prev =>
              prev.map(item =>
                item.uid === statusFile.uid
                  ? { ...item, percent: Math.min((item.percent || 0) + 10, 90) }
                  : item
              )
            )
          }, 200)

          const res: any = await difyApi.uploadFile(file)

          clearInterval(progressInterval)

          if (res.code !== 0) {
            // 更新为错误状态
            setUploadStatusFiles(prev =>
              prev.map(item =>
                item.uid === statusFile.uid
                  ? { ...item, status: 'error' as const, error: res.msg || t('chat.uploadFailed') }
                  : item
              )
            )
            message.error(`${t('chat.uploadFailed')}: ${file.name} - ${res.msg}`)
            continue // 继续处理下一个文件，而不是直接返回
          }

          // 更新为完成状态
          setUploadStatusFiles(prev =>
            prev.map(item =>
              item.uid === statusFile.uid
                ? {
                    ...item,
                    status: 'done' as const,
                    percent: 100,
                    upload_file_id: res.data.id
                  }
                : item
            )
          )

          successFiles.push(file)
          successUploads.push({
            name: file.name,
            size: file.size,
            transfer_method: 'local_file',
            upload_file_id: res.data.id,
            type: getFileTypeByName(file.name) as unknown as IFileType,
          })

          console.log(t('chat.uploadSuccess').replace('{fileName}', file.name).replace('{fileId}', res.data.id))
        } catch (error) {
          // 更新为错误状态
          setUploadStatusFiles(prev =>
            prev.map(item =>
              item.uid === statusFile.uid
                ? { ...item, status: 'error' as const, error: t('chat.uploadFailed') }
                : item
            )
          )
          console.error(`${t('chat.uploadFailed')}: ${file.name}`, error)
          message.error(`${t('chat.uploadFailed')}: ${file.name}`)
        }
      }

      // 更新状态 - 只有成功上传的文件
      if (successFiles.length > 0) {
        message.success(`${t('fileUpload.uploadSuccess')} ${successFiles.length} ${t('common.file')}`)

        // 直接添加文件，不使用FIFO队列（因为前面已经验证过数量）
        setUploadedFiles(prevFiles => [...prevFiles, ...successFiles])
        setUploadData(prevData => [...prevData, ...successUploads])

        // 调用成功回调
        onUploadSuccess?.(successFiles, successUploads)
      }
    } catch (error) {
      console.error('文件上传过程发生错误:', error)
      const errorMessage = t('chat.fileUploadError')
      message.error(errorMessage)
      onUploadError?.(errorMessage)
    } finally {
      setIsUploading(false)
    }
  }, [difyApi, maxFiles, onPreCheck, onUploadSuccess, onUploadError, t])

  const removeFile = useCallback((index: number) => {
    setUploadedFiles(prev => prev.filter((_, i) => i !== index))
    setUploadData(prev => prev.filter((_, i) => i !== index))
    setUploadStatusFiles(prev => prev.filter((_, i) => i !== index))
  }, [])

  const removeFileByUid = useCallback((uid: string) => {
    // 找到对应的索引
    const statusIndex = uploadStatusFiles.findIndex(file => file.uid === uid)
    if (statusIndex !== -1) {
      setUploadedFiles(prev => prev.filter((_, i) => i !== statusIndex))
      setUploadData(prev => prev.filter((_, i) => i !== statusIndex))
      setUploadStatusFiles(prev => prev.filter(file => file.uid !== uid))
    }
  }, [uploadStatusFiles])

  const clearFiles = useCallback(() => {
    setUploadedFiles([])
    setUploadData([])
    setUploadStatusFiles([])
  }, [])

  return {
    uploadedFiles,
    uploadData,
    uploadStatusFiles,
    isUploading,
    handleFilesUploaded,
    removeFile,
    removeFileByUid,
    clearFiles
  }
}
