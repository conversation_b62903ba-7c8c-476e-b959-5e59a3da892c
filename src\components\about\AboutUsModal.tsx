import React, { useState, useEffect, useMemo } from 'react';
import { Modal } from 'antd';
import { useSimpleTranslation } from '../../i18n/simple-hooks';
import { XAiApi } from '../../api/src/xai-api';
import { getApiConfig } from '../../utils/apiConfig';

interface AboutUsModalProps {
  open: boolean;
  onClose: () => void;
}

const AboutUsModal: React.FC<AboutUsModalProps> = ({ open, onClose }) => {
  const { t, currentLanguage } = useSimpleTranslation();
  const [aboutContent, setAboutContent] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);

  // 创建 XAiApi 实例
  const xAiApi = useMemo(() => {
    const apiConfig = getApiConfig();
    return new XAiApi({
      user: apiConfig.user,
      apiBase: apiConfig.apiBase,
      medxyToken: apiConfig.medxyToken
    });
  }, []);

  // 获取关于我们的内容
  useEffect(() => {
    const fetchAboutContent = async () => {
      if (!open) return; // 只有在模态框打开时才获取数据

      setLoading(true);
      try {
        const response = await xAiApi.getConfigByKeyFromCache({
          configKey: 'project_about'
        });

        if (response && typeof response === 'object') {
          const aboutUs = response[currentLanguage] ? response[currentLanguage] : (currentLanguage==='en'?response['en']:response['zh']);
          setAboutContent(aboutUs);
        } else {
          setAboutContent('');
        }
      } catch (error) {
        setAboutContent('');
      } finally {
        setLoading(false);
      }
    };

    fetchAboutContent();
  }, [open, currentLanguage, xAiApi]);

  return (
    <Modal
      open={open}
      onCancel={onClose}
      footer={null}
      centered
      width={500}
      zIndex={5000}
      title={t('footer.aboutUs')}
    >
        {loading ? (
          <div className="flex justify-center items-center py-8">
            <div className="text-gray-500">{t('common.loading')}</div>
          </div>
        ) : (
          /* 渲染从接口获取的HTML内容 */
          <div
            dangerouslySetInnerHTML={{ __html: aboutContent }}
          />
        )}
    </Modal>
  );
};

export default AboutUsModal;
