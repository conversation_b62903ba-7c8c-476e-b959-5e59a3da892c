import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 预渲染的路由
const routes = [
  '/',
  '/zh/novax-base',
  '/zh/novax-pro', 
  '/zh/elavax-base',
  '/zh/elavax-pro',
  '/zh/datascore-base',
  '/en/novax-base',
  '/en/novax-pro',
  '/en/elavax-base', 
  '/en/elavax-pro',
  '/en/datascore-base'
]

// 基础HTML模板
const htmlTemplate = `<!DOCTYPE html>
<html lang="{{LANG}}">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>{{TITLE}}</title>
    <meta name="description" content="{{DESCRIPTION}}" />
    <meta property="og:title" content="{{TITLE}}" />
    <meta property="og:description" content="{{DESCRIPTION}}" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="{{URL}}" />
    <meta property="og:locale" content="{{LOCALE}}" />
    <script type="module" crossorigin src="/assets/index.js"></script>
    <link rel="stylesheet" crossorigin href="/assets/index.css">
  </head>
  <body>
    <div id="root">{{PRERENDERED_CONTENT}}</div>
  </body>
</html>`

// 生成预渲染的HTML内容
function generatePrerenderedContent(route) {
  const { title, description, lang, appName } = getPageInfo(route)

  // 根据应用类型生成不同的预渲染内容
  const appDescriptions = {
    'novax-base': {
      zh: '您的灵感引擎，构建前瞻性研究蓝图',
      en: 'Your inspiration engine for building forward-looking research blueprints'
    },
    'novax-pro': {
      zh: '您的灵感引擎，构建前瞻性研究蓝图 - 专业版',
      en: 'Your inspiration engine for building forward-looking research blueprints - Pro'
    },
    'elavax-base': {
      zh: '精准剖析，深度洞察，赋能您的卓越创见',
      en: 'Precise analysis, deep insights, empowering your excellent ideas'
    },
    'elavax-pro': {
      zh: '精准剖析，深度洞察，赋能您的卓越创见 - 专业版',
      en: 'Precise analysis, deep insights, empowering your excellent ideas - Pro'
    },
    'datascore-base': {
      zh: '数据评分系统，客观评估您的研究数据',
      en: 'Data scoring system for objective evaluation of your research data'
    }
  }

  const appDesc = appDescriptions[appName]?.[lang] || description
  const welcomeText = lang === 'zh' ? '欢迎使用' : 'Welcome to'
  const inputPlaceholder = lang === 'zh' ? '请输入您的问题...' : 'Please enter your question...'

  return `
    <div style="min-height: 100vh; background-color: #f8fafc; font-family: system-ui, -apple-system, sans-serif;">
      <!-- Header -->
      <header style="background: white; border-bottom: 1px solid #e2e8f0; padding: 1rem 2rem;">
        <div style="max-width: 1200px; margin: 0 auto; display: flex; align-items: center; justify-content: space-between;">
          <h1 style="font-size: 1.5rem; font-weight: 600; color: #1e293b; margin: 0;">${title}</h1>
          <div style="color: #64748b; font-size: 0.875rem;">${lang === 'zh' ? '中文' : 'English'}</div>
        </div>
      </header>

      <!-- Main Content -->
      <main style="max-width: 1200px; margin: 0 auto; padding: 2rem;">
        <!-- Welcome Section -->
        <div style="text-center; margin-bottom: 3rem;">
          <h2 style="font-size: 2rem; font-weight: 700; color: #1e293b; margin-bottom: 1rem;">
            ${welcomeText} ${title}
          </h2>
          <p style="font-size: 1.125rem; color: #64748b; max-width: 600px; margin: 0 auto;">
            ${appDesc}
          </p>
        </div>

        <!-- Input Area -->
        <div style="background: white; border-radius: 1rem; box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1); padding: 2rem; margin-bottom: 2rem;">
          <div style="display: flex; align-items: center; gap: 0.75rem; margin-bottom: 1rem;">
            <div style="width: 2rem; height: 2rem; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 0.5rem; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">
              AI
            </div>
            <span style="font-weight: 500; color: #3b82f6;">${title}</span>
          </div>
          <div style="background: #f8fafc; border: 2px solid #e2e8f0; border-radius: 0.75rem; padding: 1rem; min-height: 120px; position: relative;">
            <div style="color: #94a3b8; font-size: 1rem;">${inputPlaceholder}</div>
          </div>
        </div>

        <!-- App Selection -->
        <div style="margin-bottom: 2rem;">
          <h3 style="font-size: 1.25rem; font-weight: 600; color: #1e293b; margin-bottom: 1rem;">
            ${lang === 'zh' ? '选择AI助手' : 'Choose AI Assistant'}
          </h3>
          <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
            ${generateAppCards(lang, appName)}
          </div>
        </div>

        <!-- Loading State -->
        <div style="text-align: center; padding: 2rem; color: #64748b;">
          <div style="font-size: 1rem;">${lang === 'zh' ? '正在加载完整功能...' : 'Loading full features...'}</div>
        </div>
      </main>
    </div>
  `
}

// 生成应用卡片
function generateAppCards(lang, currentApp) {
  const apps = [
    { name: 'novax-base', title: 'NovaX AI', type: lang === 'zh' ? '免费版' : 'Free' },
    { name: 'novax-pro', title: 'NovaX Pro', type: lang === 'zh' ? '专业版' : 'Pro' },
    { name: 'elavax-base', title: 'ElavaX AI', type: lang === 'zh' ? '免费版' : 'Free' },
    { name: 'elavax-pro', title: 'ElavaX Pro', type: lang === 'zh' ? '专业版' : 'Pro' },
    { name: 'datascore-base', title: 'DataScore AI', type: lang === 'zh' ? '基础版' : 'Base' }
  ]

  return apps.map(app => {
    const isSelected = app.name === currentApp
    return `
      <div style="background: white; border: 2px solid ${isSelected ? '#3b82f6' : '#e2e8f0'}; border-radius: 0.75rem; padding: 1rem; text-align: center; transition: all 0.2s; ${isSelected ? 'box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);' : ''}">
        <div style="font-weight: 600; color: ${isSelected ? '#3b82f6' : '#1e293b'}; margin-bottom: 0.5rem;">
          ${app.title}
        </div>
        <div style="font-size: 0.75rem; color: white; background: ${isSelected ? '#3b82f6' : '#94a3b8'}; padding: 0.25rem 0.5rem; border-radius: 0.25rem; display: inline-block;">
          ${app.type}
        </div>
      </div>
    `
  }).join('')
}

// 根据路由生成页面信息
function getPageInfo(route) {
  const segments = route.split('/').filter(Boolean)
  const lang = segments[0] || 'zh'
  const appName = segments[1] || 'novax-base'
  
  const appNames = {
    'novax-base': { zh: 'NovaX AI', en: 'NovaX AI' },
    'novax-pro': { zh: 'NovaX Pro', en: 'NovaX Pro' },
    'elavax-base': { zh: 'ElavaX AI', en: 'ElavaX AI' },
    'elavax-pro': { zh: 'ElavaX Pro', en: 'ElavaX Pro' },
    'datascore-base': { zh: 'DataScore AI', en: 'DataScore AI' }
  }
  
  const descriptions = {
    'novax-base': { 
      zh: '您的灵感引擎，构建前瞻性研究蓝图', 
      en: 'Your inspiration engine for building forward-looking research blueprints' 
    },
    'novax-pro': { 
      zh: '您的灵感引擎，构建前瞻性研究蓝图 - 专业版', 
      en: 'Your inspiration engine for building forward-looking research blueprints - Pro' 
    },
    'elavax-base': { 
      zh: '精准剖析，深度洞察，赋能您的卓越创见', 
      en: 'Precise analysis, deep insights, empowering your excellent ideas' 
    },
    'elavax-pro': { 
      zh: '精准剖析，深度洞察，赋能您的卓越创见 - 专业版', 
      en: 'Precise analysis, deep insights, empowering your excellent ideas - Pro' 
    },
    'datascore-base': { 
      zh: '数据评分系统，客观评估您的研究数据', 
      en: 'Data scoring system for objective evaluation of your research data' 
    }
  }
  
  const title = appNames[appName]?.[lang] || 'AI Assistant'
  const description = descriptions[appName]?.[lang] || 'AI Assistant for Research'
  
  return { title, description, lang, appName }
}

// 生成预渲染文件
async function generatePrerenderedFiles() {
  const distDir = path.resolve(__dirname, '../dist')

  // 确保dist目录存在
  if (!fs.existsSync(distDir)) {
    fs.mkdirSync(distDir, { recursive: true })
  }

  for (const route of routes) {
    const { title, description, lang } = getPageInfo(route)

    // 生成预渲染内容
    console.log(`正在渲染: ${route}`)
    const prerenderedContent = generatePrerenderedContent(route)

    // 生成HTML内容
    const html = htmlTemplate
      .replace(/{{LANG}}/g, lang)
      .replace(/{{TITLE}}/g, title)
      .replace(/{{DESCRIPTION}}/g, description)
      .replace(/{{URL}}/g, `https://ai.medsci.cn${route}`)
      .replace(/{{LOCALE}}/g, lang === 'zh' ? 'zh_CN' : 'en_US')
      .replace(/{{PRERENDERED_CONTENT}}/g, prerenderedContent)

    // 确定文件路径
    let filePath
    if (route === '/') {
      filePath = path.join(distDir, 'index.html')
    } else {
      const routeDir = path.join(distDir, route)
      fs.mkdirSync(routeDir, { recursive: true })
      filePath = path.join(routeDir, 'index.html')
    }

    // 写入文件
    fs.writeFileSync(filePath, html)
    console.log(`Generated: ${filePath}`)
  }

  console.log(`预渲染完成！生成了 ${routes.length} 个页面`)
}

// 执行预渲染
generatePrerenderedFiles().catch(error => {
  console.error('预渲染失败:', error)
  process.exit(1)
})
