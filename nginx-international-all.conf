server {
    listen 80;
    listen 443 ssl;
    server_name medxy.ai www.medxy.ai;

    index  index.shtml index.htm index.php index.html;
    root  /var/www/html/medxy.ai/public;

    ssl_certificate  /etc/letsencrypt/live/medxy.ai/fullchain.cer;
    ssl_certificate_key /etc/letsencrypt/live/medxy.ai/medxy.ai.key;

    ssl_session_timeout 5m;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;
    ssl_protocols TLSv1.2;
    ssl_prefer_server_ciphers on;
#---------------------------------------------------------
 if ($host !~* ^www\.) {
        return 301 https://www.medxy.ai$request_uri;
    }
if ($scheme = http) {
    return 301 https://$host$request_uri;
}
#---------------------------------------------------------
# --- 处理直接访问WordPress路径的重定向 ---
# 只处理用户直接访问wp-*路径的情况，重定向到/blog/子路径
rewrite ^/(wp-admin|wp-login\.php|wp-includes|wp-content)(.*)$ /blog/$1$2 last;

# --- 处理 /blog 路径的代理 ---
location ^~ /blog {
    # 内部重写 URI：将 /blog/path 变为 /path 发送给后端
    rewrite ^/blog(.*)$ $1 break;

    proxy_pass http://127.0.0.1:8082; # 代理到后端 Blog 服务
    proxy_http_version 1.1;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";

    # --- 简化的重定向规则（WordPress siteurl已设置为/blog/） ---
    # WordPress已知自己在/blog/路径下，会自动生成正确的URL

    # 只处理后端IP:端口的重定向到/blog/（必要的基础重定向）
    proxy_redirect http://127.0.0.1:8082/ /blog/;
    # --- proxy_redirect 规则结束 ---
}
#---------------------------------------------------------
location / {
  proxy_pass http://127.0.0.1:3000;
  proxy_http_version 1.1;
  proxy_set_header Host $host;
  proxy_set_header X-Real-IP $remote_addr;
  proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
  proxy_set_header X-Forwarded-Proto $scheme;
  proxy_set_header Upgrade $http_upgrade;
  proxy_set_header Connection "upgrade";
      
  proxy_redirect http://$host:3000/ /;
 }
      access_log /u/medsci/logs/nginx/www.medxy.ai.log access;
    }

