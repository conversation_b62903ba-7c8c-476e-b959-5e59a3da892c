import React, { useState, useEffect, useMemo, useRef } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import FloatingButtons from './FloatingButtons'
import { XAiApi, IGetAiAppInfoResponse } from '../api/src/xai-api'
import { message } from 'antd'
import { useSimpleTranslation, useI18nRouter } from '../i18n/simple-hooks'
import AiR<PERSON>ponse<PERSON>enderer from './AiResponseRenderer'

interface ChatMessage {
  id: string
  type: 'user' | 'assistant'
  content: string
  timestamp: Date
  isGenerating?: boolean
}

interface QAItem {
  query: string
  answer: string
}

interface CaseExampleProps {
  currentAppUuid: string
  xAiApi: XAiApi
}

// 支持Markdown实时渲染的打字机效果组件
const TypewriterText: React.FC<{
  text: string
  speed?: number
  onComplete?: () => void
  delay?: number
  onContentChange?: () => void
}> = ({ text, speed = 30, onComplete, delay = 0, onContentChange }) => {
  const [displayText, setDisplayText] = useState('')
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isStarted, setIsStarted] = useState(false)
  const [isCompleted, setIsCompleted] = useState(false)

  // 重置状态当文本改变时
  useEffect(() => {
    setDisplayText('')
    setCurrentIndex(0)
    setIsStarted(false)
    setIsCompleted(false)
  }, [text])

  useEffect(() => {
    if (delay > 0) {
      const delayTimer = setTimeout(() => {
        setIsStarted(true)
      }, delay)
      return () => clearTimeout(delayTimer)
    } else {
      setIsStarted(true)
    }
  }, [delay])

  useEffect(() => {
    if (!isStarted || currentIndex >= text.length) {
      if (currentIndex >= text.length && !isCompleted) {
        setIsCompleted(true)
        // 延迟一小段时间再调用完成回调，让用户看到完整内容
        setTimeout(() => {
          if (onComplete) {
            onComplete()
          }
        }, 500)
      }
      return
    }

    const timer = setTimeout(() => {
      setDisplayText(prev => prev + text[currentIndex])
      setCurrentIndex(prev => prev + 1)

      // 每隔一定字符数或遇到换行符时触发滚动
      // 这样可以减少滚动频率，同时确保长内容能正确滚动
      if (onContentChange && (
        currentIndex % 20 === 0 || // 每20个字符
        text[currentIndex] === '\n' || // 遇到换行
        currentIndex === text.length - 1 // 最后一个字符
      )) {
        onContentChange()
      }
    }, speed)

    return () => clearTimeout(timer)
  }, [currentIndex, text, speed, onComplete, isStarted, isCompleted, onContentChange])

  return (
    <div className="typewriter-container">
      <div className="relative">
        {/* 使用AiResponseRenderer实时渲染Markdown，在文本末尾添加光标字符 */}
        <AiResponseRenderer
          content={displayText + (currentIndex < text.length ? '▊' : '')}
          fontSize="lg"
        />
      </div>
    </div>
  )
}

// 自定义消息组件 - 与ChatDetail历史消息样式保持一致
const CaseMessage: React.FC<{
  message: ChatMessage
  onTypingComplete?: (messageId: string) => void
  onContentChange?: () => void
  isInstantMode?: boolean
}> = ({ message, onTypingComplete, onContentChange, isInstantMode = false }) => {
  const [showFinalRender, setShowFinalRender] = useState(false)

  // 当消息不再生成时，显示最终渲染
  useEffect(() => {
    if (!message.isGenerating) {
      setShowFinalRender(true)
    } else {
      setShowFinalRender(false)
    }
  }, [message.isGenerating])

  const handleTypingComplete = () => {
    // 打字机效果完成后，先触发一次滚动，然后通知父组件
    if (onContentChange) {
      onContentChange()
    }

    // 延迟一点再通知父组件，确保滚动完成
    setTimeout(() => {
      if (onTypingComplete) {
        onTypingComplete(message.id)
      }
    }, 100)
  }

  const handleContentChange = () => {
    // 内容变化时触发滚动
    if (onContentChange) {
      onContentChange()
    }
  }

  if (message.type === 'user') {
    return (
      <div className="mx-auto max-w-4xl relative group mb-6">
        <div className="ml-auto max-w-2xl bg-gray-0 border border-gray-200 rounded-2xl rounded-br-md p-4 shadow-sm hover:shadow-md transition-all duration-200">
          <div className="text-gray-800 text-base leading-relaxed">
            <AiResponseRenderer
              content={message.content}
              fontSize="base"
            />
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="mx-auto max-w-4xl relative group mb-6">
      <div className="mr-auto max-w-4xl">
        <div className="text-base text-gray-800 leading-relaxed prose prose-base max-w-none">
          {message.isGenerating && !isInstantMode ? (
            <TypewriterText
              text={message.content}
              speed={1}
              onComplete={handleTypingComplete}
              onContentChange={handleContentChange}
            />
          ) : showFinalRender || isInstantMode ? (
            <AiResponseRenderer
              content={message.content}
              fontSize="lg"
            />
          ) : (
            // 显示原始文本作为后备
            <div className="whitespace-pre-wrap">{message.content}</div>
          )}
        </div>
      </div>
    </div>
  )
}

// 自定义消息列表组件
const CaseMessageList: React.FC<{
  messages: ChatMessage[]
  onTypingComplete?: (messageId: string) => void
  isInstantMode?: boolean
}> = ({ messages, onTypingComplete, isInstantMode = false }) => {
  const messagesEndRef = React.useRef<HTMLDivElement>(null)
  const scrollTimeoutRef = React.useRef<NodeJS.Timeout>()

  // 防抖滚动函数
  const scrollToBottom = React.useCallback(() => {
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current)
    }

    scrollTimeoutRef.current = setTimeout(() => {
      messagesEndRef.current?.scrollIntoView({
        behavior: 'smooth',
        block: 'end'
      })
    }, 50) // 50ms 防抖延迟
  }, [])

  // 当消息数组变化时滚动
  useEffect(() => {
    scrollToBottom()
  }, [messages, scrollToBottom])

  // 清理定时器
  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current)
      }
    }
  }, [])

  // 处理内容变化时的滚动
  const handleContentChange = React.useCallback(() => {
    scrollToBottom()
  }, [scrollToBottom])

  return (
    <div className="flex-1 px-4 py-6 pb-48 overflow-y-auto bg-gray-50 pt-16 md:pt-6">
      <div className="max-w-4xl mx-auto">
        {messages.map((message) => (
          <CaseMessage
            key={message.id}
            message={message}
            onTypingComplete={onTypingComplete}
            onContentChange={handleContentChange}
            isInstantMode={isInstantMode}
          />
        ))}
        <div ref={messagesEndRef} />
      </div>
    </div>
  )
}

const CaseExample: React.FC<CaseExampleProps> = ({ xAiApi, currentAppUuid }) => {
  const { lang, caseId } = useParams()
  const navigate = useNavigate()
  const { currentLanguage } = useI18nRouter()
  const { t } = useSimpleTranslation()

  // 返回上一页
  const handleGoBack = () => {
    navigate(-1) // 返回上一页
  }
  const [messages, setMessages] = useState<ChatMessage[]>([])

  const [isProcessing, setIsProcessing] = useState(false) // 防止重复处理
  const [hasInitialized, setHasInitialized] = useState(false) // 防止重复初始化
  const [caseTitle, setCaseTitle] = useState<string>(t('case.title')) // 存储案例标题
  const [isInstantMode, setIsInstantMode] = useState(false) // 是否为即时渲染模式
  const [hasShownInstant, setHasShownInstant] = useState(false) // 是否已经显示过即时渲染
  const [currentApp, setCurrentApp] = useState<IGetAiAppInfoResponse | null>(null) // 当前应用信息

  // 用于存储定时器引用，便于清理
  const timersRef = React.useRef<ReturnType<typeof setTimeout>[]>([])
  // 用于防止重复API请求
  const isRequestingRef = React.useRef(false)

  // 获取当前应用信息
  useEffect(() => {
    const fetchCurrentApp = async () => {
      if (!currentAppUuid) {
        console.log('currentAppUuid 为空，无法获取应用信息');
        return;
      }

      try {
        const app = await xAiApi.getAppByUuid({ appUuid: currentAppUuid });
        setCurrentApp(app);
      } catch (error) {
        console.error('获取当前应用失败:', error);
        setCurrentApp(null);
      }
    };

    fetchCurrentApp();
  }, [currentAppUuid, xAiApi]);

  // 案例不存在时的处理函数
  const handleCaseNotFound = () => {
    message.error(t('case.notFound'))
    setTimeout(() => {
      navigate(lang ? `/${lang}` : '/')
    }, 3000) // 延迟3秒后跳转
  }

  // 清理定时器的函数
  const clearAllTimers = () => {
    timersRef.current.forEach(timer => clearTimeout(timer))
    timersRef.current = []
  }

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      clearAllTimers()
      isRequestingRef.current = false
    }
  }, [])

  // 当caseId改变时重置状态
  useEffect(() => {
    return () => {
      // caseId改变时的清理工作
      clearAllTimers()
      isRequestingRef.current = false
      setIsProcessing(false)
      setHasInitialized(false)
      setIsInstantMode(false)
      setHasShownInstant(false)
    }
  }, [caseId])

  // 获取案例数据
  useEffect(() => {
    if (!caseId) {
      handleCaseNotFound()
      return
    }

    // 防止重复请求
    if (isRequestingRef.current) {
      return
    }

    // 如果已经初始化过相同的caseId，跳过
    if (hasInitialized && messages.length > 0) {
      return
    }

    // 清理之前的状态
    clearAllTimers()
    setIsProcessing(false)
    setMessages([])
    setHasInitialized(false)
    setIsInstantMode(false)
    setHasShownInstant(false)

    // 调用API获取案例数据
    const fetchCaseData = async () => {
      if (isRequestingRef.current) return

      isRequestingRef.current = true
      try {
        // 调用qaList API获取案例数据
        const response = await xAiApi.qaList({articleId:"",encryptionId: caseId})

        if (response && Array.isArray(response) && response.length > 0) {
          setHasInitialized(true)

          // 提取question字段作为标题
          const firstItem = response[0]
          if (firstItem && firstItem.question) {
            setCaseTitle(firstItem.question)
          }

          generateMessagesFromCaseData(response)
        } else {
          handleCaseNotFound()
        }
      } catch (error) {
        handleCaseNotFound()
      } finally {
        isRequestingRef.current = false
      }
    }

    fetchCaseData()

  }, [caseId]) // 只依赖caseId，移除xAiApi依赖

  // 根据案例数据生成对话消息
  const generateMessagesFromCaseData = (caseData: any) => {
    // 防止重复处理
    if (isProcessing) {
      return
    }

    // 清理之前的状态，确保干净的开始
    clearAllTimers()
    setMessages([])
    setIsProcessing(true)

    let qaItems: QAItem[] = []

    try {
      // 解析API返回的数据
      if (caseData && Array.isArray(caseData) && caseData.length > 0) {
        const firstItem = caseData[0]
        if (firstItem && firstItem.answer) {
          let answerData = firstItem.answer

          // 如果 answer 是字符串，尝试解析为 JSON
          if (typeof answerData === 'string') {
            try {
              answerData = JSON.parse(answerData)
            } catch (parseError) {
              handleCaseNotFound()
              return
            }
          }

          // 如果解析成功且是数组，使用解析的数据
          if (Array.isArray(answerData) && answerData.length > 0) {
            qaItems = answerData
          } else {
            handleCaseNotFound()
            return
          }
        } else {
          handleCaseNotFound()
          return
        }
      } else {
        handleCaseNotFound()
        return
      }

      // 验证问答数据的有效性
      if (qaItems.length === 0) {
        handleCaseNotFound()
        return
      }

    } catch (error) {
      handleCaseNotFound()
      return
    }

    // 生成对话消息，按条输出
    generateQAMessages(qaItems)
  }

  // 生成问答消息，带打字机效果
  const generateQAMessages = (qaItems: QAItem[]) => {
    // 设置处理状态
    setIsProcessing(true)

    const allMessages: ChatMessage[] = []

    // 为每个问答对生成消息
    qaItems.forEach((item, index) => {
      // 用户问题
      if (item.query && item.query.trim()) {
        const userMessage: ChatMessage = {
          id: `user_${index}_${Date.now()}`,
          type: 'user',
          content: item.query.trim(),
          timestamp: new Date()
        }
        allMessages.push(userMessage)
      }

      // AI 回答
      if (item.answer && item.answer.trim()) {
        const assistantMessage: ChatMessage = {
          id: `assistant_${index}_${Date.now()}`,
          type: 'assistant',
          content: item.answer.trim(),
          timestamp: new Date(),
          isGenerating: true // 初始状态为生成中，用于打字机效果
        }
        allMessages.push(assistantMessage)
      }
    })
    // 逐条显示消息
    showMessagesSequentially(allMessages)
  }

  // 用于跟踪当前显示的消息索引
  const currentMessageIndexRef = useRef(0)
  const allMessagesRef = useRef<ChatMessage[]>([])

  // 处理查看结果按钮点击
  const handleViewResults = () => {
    if (isInstantMode) {
      // 当前是即时模式，切换到回放模式
      setIsInstantMode(false)
      // 重新开始打字机效果
      if (allMessagesRef.current.length > 0) {
        showMessagesSequentially(allMessagesRef.current)
      }
    } else {
      // 当前是打字机模式，切换到即时模式
      setIsInstantMode(true)
      setHasShownInstant(true)
      // 清理定时器
      clearAllTimers()
      setIsProcessing(false)

      // 立即显示所有消息，不带打字机效果
      if (allMessagesRef.current.length > 0) {
        const instantMessages = allMessagesRef.current.map(msg => ({
          ...msg,
          isGenerating: false // 关闭打字机效果
        }))
        setMessages(instantMessages)
      }
    }
  }

  // 处理打字机效果完成的回调
  const handleTypingComplete = (messageId: string) => {
    // 打字机效果完成后，移除生成状态
    setMessages(prev =>
      prev.map(msg =>
        msg.id === messageId
          ? { ...msg, isGenerating: false }
          : msg
      )
    )

    // 延迟后显示下一条消息
    const timer = setTimeout(() => {
      showNextMessage()
    }, 1200) // 每条消息之间间隔1.2秒
    timersRef.current.push(timer)
  }

  // 显示下一条消息
  const showNextMessage = () => {
    const currentIndex = currentMessageIndexRef.current
    const allMessages = allMessagesRef.current

    if (currentIndex >= allMessages.length) {
      // 所有消息显示完成
      setIsProcessing(false)
      return
    }

    const nextMessage = allMessages[currentIndex]
    currentMessageIndexRef.current++

    // 添加下一条消息到列表
    setMessages(prev => [...prev, nextMessage])

    // 如果是用户消息，自动显示下一条
    if (nextMessage.type === 'user') {
      const timer = setTimeout(() => {
        showNextMessage()
      }, 1000) // 用户消息后稍作停顿
      timersRef.current.push(timer)
    }
    // 如果是助手消息，等待打字机效果完成（通过handleTypingComplete回调）
  }

  // 逐条显示消息的函数
  const showMessagesSequentially = (allMessages: ChatMessage[]) => {
    if (allMessages.length === 0) {
      setIsProcessing(false)
      return
    }

    // 重置状态
    currentMessageIndexRef.current = 0
    allMessagesRef.current = allMessages
    setMessages([]) // 清空现有消息

    // 开始显示第一条消息
    showNextMessage()
  }



  return (
    <div className="relative h-screen" style={{ backgroundColor: 'var(--bg-main)' }}>
      <div className="h-screen flex flex-col">
        {/* 移动端头部 - 固定在顶部 */}
        <div className="md:hidden fixed top-0 left-0 right-0 z-50 bg-white border-b border-gray-200 px-4 py-3 shadow-sm">
          <div className="flex items-center">
            <button
              onClick={handleGoBack}
              className="flex items-center justify-center w-8 h-8 rounded-full hover:bg-gray-100 transition-colors mr-3"
            >
              <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            <div className="flex items-center flex-1">
              <div className="w-8 h-8 bg-gradient-to-r rounded-full flex items-center justify-center mr-3">
                <img
                  src={currentApp?.appIcon}
                  alt={currentApp?.appName}
                  className="w-5 h-5 rounded-full"
                />
              </div>
              <span className="text-lg font-semibold text-gray-800">{currentApp?.appName}</span>
            </div>

            {/* 价格 - 移动端 */}
            <button
              onClick={handleViewResults}
              className="flex items-center justify-center px-3 py-1.5 bg-blue-500 hover:bg-blue-600 text-white text-sm rounded-xl transition-colors duration-200"
              title={isInstantMode ? t('case.viewReplay') : t('case.viewResults')}
            >
              <span>{isInstantMode ? t('case.viewReplay') : t('case.viewResults')}</span>
            </button>
          </div>
        </div>

        {/* 桌面端案例标题 */}
        <div className="hidden md:block bg-white border-b border-gray-200 px-6 py-4 shadow-sm">
          <div className="max-w-4xl mx-auto">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="flex flex-col items-center">
                  <div className="w-10 h-10 bg-gradient-to-r rounded-full flex items-center justify-center text-white font-medium mb-1">
                    <img
                      src={currentApp?.appIcon}
                      alt={currentApp?.appName}
                      className="w-6 h-6 rounded-full"
                    />
                  </div>
                  <span className="text-xs text-gray-600 font-medium text-center block w-full">{currentApp?.appName}</span>
                </div>
                <div>
                  <h1 className="text-lg font-semibold text-gray-800">
                    {caseTitle}
                  </h1>
                  <p className="text-sm">
                    {t('case.caseId')} <span className="text-blue-600 font-medium">{caseId}</span>
                  </p>
                </div>
              </div>

              {/* 按钮 - 桌面端 */}
              <button
                onClick={handleViewResults}
                className="flex items-center justify-center px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white text-sm rounded-xl transition-colors duration-200 shadow-sm hover:shadow-md"
                title={isInstantMode ? t('case.viewReplay') : t('case.viewResults')}
              >
                <span>{isInstantMode ? t('case.viewReplay') : t('case.viewResults')}</span>
              </button>
            </div>
          </div>
        </div>

        {/* 消息列表区域 */}
        <CaseMessageList
          messages={messages}
          onTypingComplete={handleTypingComplete}
          isInstantMode={isInstantMode}
        />
      </div>

      {/* 悬浮按钮 - 固定在屏幕左侧，不显示新建会话按钮 */}
      <div className="hidden md:block">
        <FloatingButtons showNewChat={false} />
      </div>
    </div>
  )
}

export default CaseExample
