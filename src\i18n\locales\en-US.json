{"common": {"loading": "Loading...", "error": "Error", "success": "Success", "cancel": "Cancel", "confirm": "Confirm", "back": "Back", "save": "Save", "delete": "Delete", "edit": "Edit", "close": "Close", "next": "Next", "previous": "Previous", "submit": "Submit", "reset": "Reset", "search": "Search", "filter": "Filter", "sort": "Sort", "refresh": "Refresh", "copy": "Copy", "download": "Download", "upload": "Upload", "share": "Share", "settings": "Settings", "help": "Help", "about": "About", "logout": "Logout", "login": "<PERSON><PERSON>"}, "header": {"title": "NovaX AI", "menu": "<PERSON><PERSON>", "user": "User", "language": "Language", "version": "Version", "subscription": "Subscription", "profile": "Profile", "switchLanguage": "Switch Language", "chinese": "中文", "english": "English"}, "home": {"welcome": "Welcome to NovaX AI", "subtitle": "Your Research Inspiration Engine", "description": "Designed for building forward-looking research blueprints", "getStarted": "Get Started", "learnMore": "Learn More", "features": "Features", "services": "Services", "inspirationEngine": "Your inspiration engine for building forward-looking research blueprints", "analysisDescription": "Precise analysis, deep insights, empowering your exceptional innovations"}, "chat": {"title": "Cha<PERSON>", "newChat": "New Chat", "chatHistory": "Chat History", "inputPlaceholder": "Please enter your question...", "send": "Send", "stop": "Stop", "regenerate": "Regenerate", "copy": "Copy", "delete": "Delete", "thinking": "Thinking...", "generating": "Generating...", "searching": "Searching...", "browsing": "Browsing...", "reviewing": "Reviewing...", "secondReviewing": "Second Reviewing...", "workflow": "Workflow", "thinkingProcess": "Thinking Process", "thinkingChain": "Thinking Chain", "searchResults": "Search Results", "browsingResults": "Browsing Results", "finalAnswer": "Final Answer", "clickToCollapse": "Click to collapse", "clickToExpand": "Click to expand", "clickToSync": "Click to sync scroll to corresponding step on the left", "thinkingCollapsed": "Thinking process collapsed, click to show", "browsingCollapsed": "Browsing results collapsed, click to show", "reviewCollapsed": "Review process collapsed, click to show", "secondReviewCollapsed": "Second review process collapsed, click to show", "nodeDataCollapsed": "Node data collapsed, click to show", "characters": "characters", "steps": "steps", "sources": "sources", "nodeLoading": "Node Loading", "expandDetails": "Expand Details", "collapseDetails": "Collapse Details", "loading": "Loading...", "completed": "Answer completed", "preparing": "Preparing", "executionFailed": "execution failed", "reviewingFinalResult": "is reviewing the final result", "secondReviewingFinalResult": "is conducting second review of the final result", "workflowRunning": "Workflow Running", "workflowFinished": "Workflow Finished", "workflowError": "Workflow Error", "uploadFile": "Upload File", "fileUploaded": "File Uploaded", "fileUploadError": "File Upload Failed", "maxFilesReached": "Maximum file limit reached ({count} files)", "maxFilesExceeded": "Can only select {available} more files, total cannot exceed {max} files", "batchUploadLimit": "Can only select up to 2 files at a time", "unsupportedFileType": "File \"{fileName}\" format not supported, supported formats: {formats}", "fileSizeExceeded": "File \"{fileName}\" exceeds size limit ({maxSize}MB)", "fileUploadNotSupported": "File upload is not supported for this app", "appNotInitialized": "App not initialized, please try again later", "uploadFailed": "File upload failed", "networkError": "Network connection error", "uploadTimeout": "File upload timeout", "fileSizeLimit": "File size exceeds limit", "uploadSuccess": "File upload successful", "noAuthTokenWarning": "No authentication token found", "noAuthToken": "Authentication token missing", "initializationFailed": "Initialization failed", "authFailed": "Authentication failed", "refreshInitFailed": "Refresh initialization failed", "unknownFile": "Unknown file", "historyResponseFormatError": "History response format error", "historyLoadFailed": "History loading failed", "loadHistoryFailed": "Failed to load history", "sendMessageFailed": "Failed to send message", "stopTask": "Stop task", "initializing": "Initializing...", "reload": "Reload", "loadingHistory": "Loading history...", "retry": "Retry"}, "sidebar": {"conversations": "Conversations", "newConversation": "New Conversation", "searchConversations": "Search Conversations", "noConversations": "No conversations", "deleteConversation": "Delete Conversation", "renameConversation": "Rename Conversation", "conversationDeleted": "Conversation deleted", "conversationRenamed": "Conversation renamed"}, "apps": {"novaxBase": {"name": "NovaX Base", "description": "Core Idea Inspiration", "subtitle": "Single research direction design based on research background"}, "novaxPro": {"name": "NovaX Pro", "description": "Personalized Research Planning", "subtitle": "Deep matching based on current research foundation and available conditions"}, "novaxUltra": {"name": "NovaX Ultra", "description": "Building Innovation Ecosystem", "subtitle": "Strategic topic clusters, beyond single ideas"}}, "services": {"title": "Service System", "base": {"title": "NovaX Base - Core Idea Inspiration", "description": "Single research direction design based on research background, get initial conception of innovative ideas, light up your research starting point."}, "pro": {"title": "NovaX Pro - Personalized Research Planning", "description": "Deep matching based on current research foundation and available conditions, providing personalized research design solutions and reasonable scientific research implementation planning."}, "ultra": {"title": "NovaX Ultra - Building Innovation Ecosystem", "description": "Strategic topic clusters, beyond single ideas, intelligent planning of multi-point linkage, sustainable development series of innovative research topic groups."}, "advantages": {"title": "Core Advantages", "aiDriven": "AI-Driven: Based on advanced artificial intelligence technology", "personalMatch": "Personal Match: Deep understanding of user research background", "forwardInsight": "Forward Insight: Grasp future development trends"}}, "subscription": {"title": "Subscription Management", "currentPlan": "Current Plan", "upgrade": "Upgrade", "renew": "<PERSON>w", "expired": "Expired", "active": "Active", "inactive": "Inactive", "subscribe": "Subscribe", "unsubscribe": "Unsubscribe", "medsciAccount": "Medsci Account"}, "errors": {"networkError": "Network connection error", "serverError": "Server error", "unauthorized": "Unauthorized access", "forbidden": "Access forbidden", "notFound": "Page not found", "validationError": "Input validation failed", "uploadError": "Upload failed", "downloadError": "Download failed", "loginRequired": "Please login first", "subscriptionRequired": "Subscription required", "insufficientPermissions": "Insufficient permissions"}, "messages": {"saveSuccess": "Save successful", "deleteSuccess": "Delete successful", "updateSuccess": "Update successful", "operationSuccess": "Operation successful", "operationFailed": "Operation failed", "confirmDelete": "Confirm to delete this item?", "unsavedChanges": "There are unsaved changes, confirm to leave?", "processingRequest": "Processing request...", "requestCompleted": "Request completed", "requestFailed": "Request failed"}, "workflow": {"nodes": {"start": "Start Node", "ifElse": "Conditional Judgment", "documentExtractor": "Document Extraction", "tool": "Tool Call", "llm": "AI Analysis", "answer": "Generate Answer", "assigner": "Variable Assignment", "variableAggregator": "Data Aggregation", "questionClassifier": "Question Classification", "code": "Code Execution", "templateTransform": "Template Transform", "httpRequest": "HTTP Request", "parameterExtractor": "Parameter Extraction"}, "status": {"running": "Running", "success": "Success", "error": "Error", "finished": "Finished", "preparing": "Preparing"}}, "fileUpload": {"title": "File Upload", "dragAndDrop": "Drag and drop files here or click to upload", "dropToUpload": "Drop files to upload", "selectFiles": "Select Files", "supportedFormats": "Supported Formats", "maxFileSize": "<PERSON> Si<PERSON>", "maxFiles": "Max upload: {count} files", "multiFileSupport": "Multiple files supported", "uploading": "Uploading...", "uploadSuccess": "Upload Successful", "uploadFailed": "Upload Failed", "removeFile": "Remove File", "fileName": "File Name", "fileSize": "File Size", "fileType": "File Type"}, "auth": {"loginRequired": "Please login first", "loginButton": "<PERSON><PERSON>", "logoutButton": "Logout", "userProfile": "User Profile", "loggedIn": "Logged In", "notLoggedIn": "Not Logged In", "loginSuccess": "Login Successful", "logoutSuccess": "Logout Successful"}, "navigation": {"home": "Home", "chat": "Cha<PERSON>", "history": "History", "settings": "Settings", "about": "About", "feedback": "<PERSON><PERSON><PERSON>", "export": "Export Data", "import": "Import Data"}, "feedback": {"titleLabel": "Feedback Title", "titlePlaceholder": "Please enter feedback title", "titleRequired": "Please enter feedback title", "titleMaxLength": "Title cannot exceed 100 characters", "contentLabel": "Feedback Content", "contentPlaceholder": "Please describe the issue or suggestion in detail. We value every feedback.", "contentRequired": "Please enter feedback content", "contentMinLength": "Feedback content must be at least 10 characters", "contentMaxLength": "Feedback content cannot exceed 1000 characters", "submit": "Submit <PERSON>", "submitSuccess": "<PERSON><PERSON><PERSON> submitted successfully. Thank you for your valuable input!", "submitError": "Submission failed, please try again later", "loginRequired": "Please login first to submit feedback"}, "cases": {"title": "Case Examples", "description": "View usage cases and examples", "viewCase": "View Case", "tryExample": "Try this example", "moreExamples": "More examples", "noCases": "No relevant cases available"}, "payment": {"title": "Payment & Subscription", "currentPlan": "Current Plan", "choosePlan": "Choose <PERSON>", "payNow": "Pay Now", "paymentSuccess": "Payment Successful", "paymentFailed": "Payment Failed", "subscriptionExpired": "Subscription Expired", "renewSubscription": "Renew Subscription", "cancelSubscription": "Cancel Subscription", "upgradeSubscription": "Upgrade Subscription"}, "mobile": {"menu": "<PERSON><PERSON>", "close": "Close", "openSidebar": "Open Sidebar", "closeSidebar": "Close Sidebar"}, "pageTitle": {"separator": " - ", "suffix": "AI Assistant", "home": "Home", "chat": "Cha<PERSON>", "newChat": "New Chat", "loading": "Loading", "error": "Error", "notFound": "Page Not Found", "apps": {"novax-base": "NovaX Base", "novax-pro": "NovaX Pro", "novax-ultra": "NovaX Ultra", "elavax-base": "ElaVaX Base", "elavax-pro": "ElaVaX Pro", "elavax-ultra": "ElaVaX Ultra"}, "templates": {"home": "{appName}{separator}{suffix}", "chat": "{chatTitle}{separator}{appName}{separator}{suffix}", "newChat": "{newChat}{separator}{appName}{separator}{suffix}", "loading": "{loading}{separator}{appName}{separator}{suffix}", "error": "{error}{separator}{appName}{separator}{suffix}", "notFound": "{notFound}{separator}{suffix}"}}}