import React, { useEffect, useMemo } from 'react'
import { Helmet } from 'react-helmet-async'
import { useSimpleTranslation } from '../i18n/simple-hooks'

export interface PageTitleProps {
  /** 页面类型 */
  pageType?: 'home' | 'chat' | 'newChat' | 'loading' | 'error' | 'notFound' | 'login' | 'signup'
  /** 应用名称键 */
  appKey?: string
  /** 自定义标题（优先级最高） */
  customTitle?: string
  /** 对话标题（用于对话页面） */
  chatTitle?: string
  /** 是否显示应用名称 */
  showAppName?: boolean
  /** 是否显示后缀 */
  showSuffix?: boolean
  /** 自定义分隔符 */
  separator?: string
  /** 最大标题长度（超出会截断） */
  maxLength?: number
  /** 自定义描述 */
  customDescription?: string
  /** 自定义关键词 */
  customKeywords?: string
  /** 自定义图片URL */
  customImage?: string
  /** 自定义URL（用于canonical和og:url） */
  customUrl?: string
  /** 是否为文章类型（影响og:type） */
  isArticle?: boolean
}

/**
 * 页面标题组件
 * 使用react-helmet-async管理document.title
 * 支持国际化、动态更新、SEO优化
 */
const PageTitle: React.FC<PageTitleProps> = ({
  pageType = 'home',
  appKey,
  customTitle,
  chatTitle,
  showAppName = true,
  showSuffix = true,
  separator,
  maxLength = 60,
  customDescription,
  customKeywords,
  customImage,
  customUrl,
  isArticle = false
}) => {
  const { t } = useSimpleTranslation()

  // 生成页面标题
  const title = useMemo(() => {
    // 如果有自定义标题，直接使用
    if (customTitle) {
      return truncateTitle(customTitle, maxLength)
    }

    // 获取翻译值
    const pageTitleSeparator = separator || t('pageTitle.separator')
    const suffix = t('pageTitle.suffix')

    // 获取应用名称
    const appName = appKey ? t(`pageTitle.apps.${appKey}`) : ''

    // 根据页面类型生成标题
    let titleParts: string[] = []

    switch (pageType) {
      case 'home':
        if (showAppName && appName) {
          titleParts.push(appName)
        }
        break

      case 'chat':
        if (chatTitle) {
          titleParts.push(chatTitle)
        } else {
          titleParts.push(t('pageTitle.chat'))
        }
        if (showAppName && appName) {
          titleParts.push(appName)
        }
        break

      case 'newChat':
        titleParts.push(t('pageTitle.newChat'))
        if (showAppName && appName) {
          titleParts.push(appName)
        }
        break

      case 'loading':
        titleParts.push(t('pageTitle.loading'))
        if (showAppName && appName) {
          titleParts.push(appName)
        }
        break

      case 'error':
        titleParts.push(t('pageTitle.error'))
        if (showAppName && appName) {
          titleParts.push(appName)
        }
        break

      case 'notFound':
        titleParts.push(t('pageTitle.notFound'))
        break

      case 'login':
        titleParts.push(t('pageTitle.login'))
        break

      case 'signup':
        titleParts.push(t('pageTitle.signup'))
        break

      default:
        if (showAppName && appName) {
          titleParts.push(appName)
        }
    }

    // 添加后缀
    if (showSuffix) {
      titleParts.push(suffix)
    }

    // 组合标题
    const finalTitle = titleParts.join(pageTitleSeparator)

    return truncateTitle(finalTitle, maxLength)
  }, [
    customTitle,
    pageType,
    appKey,
    chatTitle,
    showAppName,
    showSuffix,
    separator,
    maxLength,
    t
  ])

  // 生成页面描述
  const description = useMemo(() => {
    if (customDescription) {
      return customDescription
    }

    // 根据应用和页面类型生成描述
    const appName = appKey ? t(`pageTitle.apps.${appKey}`) : ''

    switch (pageType) {
      case 'home':
        if (appKey) {
          // 尝试获取应用特定描述，如果不存在则使用默认描述
          const appDesc = t(`seo.descriptions.${appKey}`)
          return appDesc.includes('seo.descriptions.') ? t('seo.descriptions.default') : appDesc
        }
        return t('seo.descriptions.default')
      case 'chat':
        return t('seo.descriptions.chat').replace('{appName}', appName)
      case 'newChat':
        return t('seo.descriptions.newChat').replace('{appName}', appName)
      default:
        return t('seo.descriptions.default')
    }
  }, [customDescription, pageType, appKey, t])

  // 生成关键词
  const keywords = useMemo(() => {
    if (customKeywords) {
      return customKeywords
    }

    const baseKeywords = t('seo.keywords.base').split(',')
    let appKeywords: string[] = []

    if (appKey) {
      const appKeywordStr = t(`seo.keywords.${appKey}`)
      // 如果翻译键不存在，会返回键本身，我们需要检查这种情况
      if (!appKeywordStr.includes('seo.keywords.')) {
        appKeywords = appKeywordStr.split(',').filter(Boolean)
      }
    }

    return [...baseKeywords, ...appKeywords].join(',')
  }, [customKeywords, appKey, t])

  // 生成图片URL
  const imageUrl = useMemo(() => {
    if (customImage) {
      return customImage
    }

    // 根据应用返回对应的图片
    if (appKey) {
      const appImage = t(`seo.images.${appKey}`)
      return appImage.includes('seo.images.') ? t('seo.images.default') : appImage
    }

    return t('seo.images.default')
  }, [customImage, appKey, t])

  // 生成页面URL
  const pageUrl = useMemo(() => {
    if (customUrl) {
      return customUrl
    }

    // 构建当前页面的完整URL
    const baseUrl = 'https://ai.medsci.cn'
    const currentPath = window.location.pathname

    return `${baseUrl}${currentPath}`
  }, [customUrl])

  // 截断标题函数
  function truncateTitle(title: string, maxLen: number): string {
    if (title.length <= maxLen) {
      return title
    }
    
    // 智能截断：尽量保留完整的词语
    const truncated = title.substring(0, maxLen - 3)
    const lastSpace = truncated.lastIndexOf(' ')
    const lastSeparator = truncated.lastIndexOf(' - ')
    
    // 如果找到分隔符，在分隔符处截断
    if (lastSeparator > 0 && lastSeparator > truncated.length * 0.6) {
      return truncated.substring(0, lastSeparator) + '...'
    }
    
    // 如果找到空格，在空格处截断
    if (lastSpace > 0 && lastSpace > truncated.length * 0.7) {
      return truncated.substring(0, lastSpace) + '...'
    }
    
    // 否则直接截断
    return truncated + '...'
  }

  // 调试日志
  useEffect(() => {
    if (import.meta.env.DEV) {
      console.log('[PageTitle] Title updated:', title)
    }
  }, [title])

  return (
    <Helmet>
      {/* 基础SEO标签 */}
      <title>{title}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords} />

      {/* Open Graph 标签 */}
      <meta property="og:title" content={title} />
      <meta property="og:description" content={description} />
      <meta property="og:type" content={isArticle ? "article" : "website"} />
      <meta property="og:url" content={pageUrl} />
      <meta property="og:image" content={imageUrl} />
      <meta property="og:site_name" content="梅斯小智" />

      {/* Twitter Card 标签 */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={title} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={imageUrl} />

      {/* 额外的SEO标签 */}
      <meta name="robots" content="index, follow" />
      <link rel="canonical" href={pageUrl} />

      {/* 移动端优化 */}
      <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
      <meta name="format-detection" content="telephone=no" />
    </Helmet>
  )
}

export default PageTitle
