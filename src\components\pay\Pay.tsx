import React, { useState, useEffect, useRef } from 'react'
import { XAiApi, IGetAiAppInfoResponse, PackageByKey, FeeType, PurchaseRecord, AppUser } from '../../api/src/xai-api'
import Cookies from 'js-cookie'
import { message, Modal } from 'antd'
import { QRCodeCanvas } from 'qrcode.react'
import { formatPeriodText } from '../../api/src/utils'
import { useSimpleTranslation, useI18nRouter } from '../../i18n/simple-hooks'
import { VIPIcon, DiamondCrownIcon, IMAGE_URLS } from '../icons/Icons'
import CrownBorder from '../subscription-status/CrownBorder'
import FeedbackModal from '../FeedbackModal'

// 公共支付界面组件
interface PaymentInterfaceProps {
  selectedFee: FeeType | undefined
  selectedPeriodText: string
  username: string
  xAiApi: XAiApi
  subscribe: (feeType: FeeType) => Promise<void>
  paymentLoading: boolean
  payUrl: string
  showFeedbackModal: boolean
  setShowFeedbackModal: (show: boolean) => void
}

// 可以订阅：未订阅(0)、已过期(2)、无订阅信息、已订阅免费
const PaymentInterface: React.FC<PaymentInterfaceProps> = ({
  selectedFee,
  selectedPeriodText,
  username,
  xAiApi,
  subscribe,
  paymentLoading,
  payUrl,
  showFeedbackModal,
  setShowFeedbackModal
}) => {
  const { t } = useSimpleTranslation()
  const { currentLanguage } = useI18nRouter()
  const [loading, setLoading] = useState(false)
  const [agreementChecked, setAgreementChecked] = useState(false)
  const [showQrCode, setShowQrCode] = useState(false)
  const [purchaseRecords, setPurchaseRecords] = useState<PurchaseRecord[]>([])

  // 获取购买记录
  const fetchPurchaseRecords = async () => {
    try {
      // 这里调用获取购买记录的API
      const response = await xAiApi.getPurchaseRecords()
      setPurchaseRecords(response || [])
    } catch (error) {
      console.error('获取购买记录失败:', error)
    }
  }

  // 跳转到协议
  const toAgreement = () => {
    window.open("https://www.medsci.cn/about/index.do?id=27")
  }

  // 组件初始化时获取购买记录
  useEffect(() => {
    fetchPurchaseRecords()
  }, [])

  useEffect(() => {
    // 套餐切换时隐藏二维码
    setShowQrCode(false)
    // 只有在中文状态下才自动触发订阅，英文状态下需要用户手动点击按钮
    if (!paymentLoading && selectedFee) {
        if (currentLanguage === 'zh') {
          subscribe(selectedFee)
        } else {
          if (selectedFee.coinType!='人民币' && !selectedFee.feePrice) {
            subscribe(selectedFee)
          }
        }
    }
  }, [selectedFee, currentLanguage])

  // 如果是免费套餐，显示免费订阅按钮
  if (selectedFee?.type === '免费') {
    return (
      <div className={`text-center ${selectedFee?.type === '免费' ? 'mt-8' : ''}`}>
        <button
          onClick={() => subscribe(selectedFee)}
          disabled={paymentLoading}
          className="px-8 py-3 bg-blue-500 text-white rounded-full font-medium transition-colors disabled:opacity-50"
        >
          {paymentLoading ? t('subscription.subscribingProgress') : t('subscription.subscribeNow')}
        </button>
      </div>
    )
  }

  // 付费套餐显示完整的支付界面
  return (
    <div className="flex items-start">
      {/* 根据语言显示不同内容 */}
      {currentLanguage === 'zh' ? (
        // 中文：显示完整的支付界面
        <>
          <div
            className="w-32 h-32 bg-gray-100 border border-gray-300 rounded mr-4 flex items-center justify-center cursor-pointer hover:bg-gray-200 transition-colors mt-2"
          >
            {!loading && payUrl && (
              <div className="qr-code">
                <QRCodeCanvas
                  value={payUrl}
                  size={135}
                  fgColor="#000"
                  level="H" // 纠错等级高，支持中心图片
                  imageSettings={{
                    src: IMAGE_URLS.ZFB_ICON,
                    x: undefined,
                    y: undefined,
                    height: 30,
                    width: 30,
                    excavate: true,
                  }}
                />
              </div>
            )}
          </div>
          <div className="flex-1">
            <div className="flex items-center mb-3">
              <span className="text-gray-700 font-medium">{t('subscription.alipaySupport')}</span>
            </div>

            {/* 价格显示 */}
            <div className="mb-3 text-left">
              <div className="flex items-baseline">
                <span className="text-orange-500 text-2xl font-bold">{selectedFee?.coinType=='人民币'?'¥':'$'}{selectedFee?.feePrice}</span>
                <span className="text-gray-600 ml-1">{selectedPeriodText}</span>
              </div>
            </div>

            {/* 提示信息 */}
            <div className="text-left">
              <p className="text-gray-400 text-sm mb-1">{t('subscription.medsciAccount')}: {username}</p>
              <p
                className="text-blue-500 text-sm cursor-pointer hover:underline"
                onClick={toAgreement}
              >
                {t('subscription.readAndAgree')} &gt;
              </p>
            </div>
          </div>
        </>
      ) : (
          // 显示订阅请求按钮
          <div className="flex-1 flex flex-col items-center justify-center py-4 px-4">
            {/* 价格显示 */}
            <div className="mb-3 text-center">
              <div className="flex items-baseline justify-center">
                <span className="text-orange-500 text-2xl font-bold">{selectedFee?.coinType=='人民币'?'¥':'$'} {selectedFee?.feePrice}</span>
                <span className="text-gray-600 ml-2">{selectedPeriodText}</span>
              </div>
            </div>

            {/* 账号信息 */}
            <div className="mb-3 text-center">
              <p className="text-gray-500 text-xs">{t('subscription.medsciAccount')}: <span className="font-medium text-gray-700">{username}</span></p>
            </div>

            {/* 协议选择 */}
            <div className="mb-3 flex items-center justify-center space-x-2">
              <label className="flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={agreementChecked}
                  onChange={(e) => setAgreementChecked(e.target.checked)}
                  className="w-3 h-3 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-1"
                />
              </label>
              <button
                className="text-blue-500 text-xs hover:underline focus:outline-none"
                onClick={toAgreement}
              >
                {t('subscription.readAndAgree')}
              </button>
            </div>

            {/* 订阅按钮 */}
            <button
              onClick={() => {
                if (agreementChecked) {
                  subscribe(selectedFee!)
                } else {
                  // 可以添加提示用户需要同意协议的逻辑
                  message.warning(t('subscription.agreeToTerms'))
                }
              }}
              disabled={!agreementChecked}
              className={`px-6 py-2 text-sm font-medium rounded-full transition-all duration-200 ${
                agreementChecked
                  ? 'bg-blue-500 hover:bg-blue-600 text-white shadow-sm hover:shadow-md'
                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'
              }`}
            >
              {t('subscription.confirmPayment')}
            </button>
          </div>
        )}

      <div className="flex flex-col ml-4 w-80 relative">
        {/* 滚动的订阅信息 */}
        <div className="w-80 h-32 bg-gray-50 border border-gray-200 rounded-2xl overflow-hidden relative mb-1 transition-all duration-300 ease-in-out">
          <div className="animate-scroll-vertical flex flex-col">
            {/* 第一组数据 */}
            {purchaseRecords.map((record, index) => (
              <div key={`first-${index}`} className="text-sm text-gray-600 whitespace-nowrap py-2">
                {record.userName} {t('subscription.justBought')} {record.appName} {t('subscription.member')}
              </div>
            ))}
            {/* 第二组数据 - 重复第一组实现无缝循环 */}
            {purchaseRecords.map((record, index) => (
              <div key={`second-${index}`} className="text-sm text-gray-600 whitespace-nowrap py-2">
                {record.userName} {t('subscription.justBought')} {record.appName} {t('subscription.member')}
              </div>
            ))}
          </div>
        </div>

        {/* 支付问题文案 - 根据语言区分处理方式 */}
        <div
          className="text-xs text-blue-500 text-center leading-tight mb-2 cursor-pointer hover:text-blue-700 transition-colors rounded-lg px-2 py-1"
          onClick={() => {
            if (currentLanguage === 'zh') {
              setShowQrCode(!showQrCode)
            } else {
              setShowFeedbackModal(true)
            }
          }}
        >
          <img className="w-6 h-6 inline-block" src={IMAGE_URLS.PAYMENT_HELPER} alt="扫码添加小助手" />{t('subscription.paymentProblem')}
        </div>

        {/* 助手二维码 - 浮动覆盖在滚动区域上 */}
        {showQrCode && (
          <>
            {/* 点击遮罩层 - 点击外部区域隐藏二维码 */}
            <div
              className="fixed inset-0 z-40"
              onClick={() => setShowQrCode(false)}
            />
            {/* 二维码容器 */}
            <div
              className="absolute top-0 left-1/2 transform -translate-x-1/2 w-32 h-32 bg-white border-2 border-gray-300 rounded-3xl shadow-lg flex items-center justify-center z-50 transition-all duration-300 ease-in-out"
              onClick={(e) => e.stopPropagation()}
            >
              <img src={IMAGE_URLS.CUSTOMER_SERVICE_QR}
                alt="扫码添加小助手"
                className="w-28 h-28 object-contain rounded-2xl"
              />
            </div>
          </>
        )}
      </div>
    </div>
  )
}

interface SubscriptionModalProps {
  isOpen: boolean
  onClose: () => void
  xAiApi: XAiApi
  currentApp: IGetAiAppInfoResponse
  subStatusDetail: PackageByKey
  appUser: AppUser | null
  subscribe: (feeType: FeeType) => Promise<void>
  paymentLoading: boolean
  payUrl: string
  clearPaymentTimer?: () => void
}

const Pay: React.FC<SubscriptionModalProps> = ({ isOpen, onClose, xAiApi, currentApp, subStatusDetail, appUser, subscribe, paymentLoading, payUrl, clearPaymentTimer }) => {
  const { t } = useSimpleTranslation()
  // 辅助函数：获取当前应用的订阅等级
  const getCurrentAppTier = (): 'base' | 'pro' | 'ultra' | null => {
    if (!currentApp || !subStatusDetail) return null

    const subStatus = subStatusDetail.subStatus
    if (subStatus !== 1 && subStatus !== 3) return null // 未订阅或已过期

    if (currentApp.appNameEn.includes('base')) return 'base'
    if (currentApp.appNameEn.includes('pro')) return 'pro'
    if (currentApp.appNameEn.includes('ultra')) return 'ultra'

    return null
  }

  // 辅助函数：根据等级获取虚线边框样式
  const getTierBorderStyle = (tier: 'base' | 'pro' | 'ultra' | null): string => {
    if (!tier) return ''

    switch (tier) {
      case 'base':
        return 'border-2 border-dashed border-gray-400' // 白银色虚线
      case 'pro':
        return 'border-2 border-dashed border-yellow-500' // 黄金色虚线
      case 'ultra':
        return 'border-2 border-dashed border-purple-500' // 钻石色虚线
      default:
        return ''
    }
  }
  const [selectedFee, setSelectedFee] = useState<FeeType>()
  const [selectedPeriodText, setSelectedPeriodText] = useState<string>(t('subscription.perMonth'))
  const [userInfoString, setUserInfoString] = useState(Cookies.get('userInfo'))
  const [userInfo, setUserInfo] = useState(() => {
    return userInfoString ? JSON.parse(userInfoString) : null
  })
  const [avatar, setAvatar] = useState(userInfo?.avatar || IMAGE_URLS.DEFAULT_AVATAR)
  const [username, setUsername] = useState(userInfo?.userName || '')
  const [showCustomerService, setShowCustomerService] = useState(false)
  const [showFeedbackModal, setShowFeedbackModal] = useState(false)
  const { currentLanguage } = useI18nRouter()

  // 当应用切换时重置选择状态
  useEffect(() => {
    setSelectedFee(undefined)
  }, [currentApp?.appUuid])

  // 当弹框打开时初始化选择
  useEffect(() => {
    if (isOpen && subStatusDetail) {
      initializeSelection()
    }
  }, [isOpen, subStatusDetail])

  // ESC键关闭弹窗功能
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown)
      return () => {
        document.removeEventListener('keydown', handleKeyDown)
      }
    }
  }, [isOpen, onClose])

  // 初始化选择第一个套餐
  const initializeSelection = () => {
    const fee = subStatusDetail?.feeTypes.find(item => item.type === subStatusDetail.packageType)
    handlePlanSelect(fee ? fee : subStatusDetail.feeTypes[0])
  }

  const handlePlanSelect = (feeType: FeeType) => {
    // 切换套餐时清除当前的支付定时器
    if (clearPaymentTimer) {
      clearPaymentTimer()
      console.log('切换套餐，已清除支付状态轮询定时器')
    }

    setSelectedFee(feeType)
    const periodText = formatPeriodText(feeType.monthNum, feeType.periodType, t);
    setSelectedPeriodText(periodText);
  }

  // 获取套餐类型的国际化名称
  const getPlanTypeName = (type: string): string => {
    const typeLower = type.toLowerCase()
    if (currentLanguage == 'zh') {
      if (typeLower === '免费' || typeLower === 'free') {
        return t('subscription.free')
      } else if (typeLower.includes('周') || typeLower.includes('week')) {
        return t('subscription.weeklySubscription')
      } else if (typeLower.includes('月') || typeLower.includes('month')) {
        return t('subscription.monthlySubscription')
      }
    } else {
      if (typeLower === '免费' || typeLower === 'free') {
        return t('subscription.free')
      } else if (typeLower.includes('月') || typeLower.includes('month')) {
        return t('subscription.monthlySub')
      } else if (typeLower.includes('季') || typeLower.includes('quarter')) {
        return t('subscription.quarterlySub')
      } else if (typeLower.includes('年') || typeLower.includes('year')) {
        return t('subscription.yearlySub')
      }
    }

    return type // 如果没有匹配的翻译，返回原始类型
  }

  if (!isOpen) return null

  const errorImg = (e: React.SyntheticEvent<HTMLImageElement, Event>) => {
    console.log('=====', '用户头像加载失败，使用默认头像')
    const target = e.target as HTMLImageElement
    target.src = IMAGE_URLS.DEFAULT_AVATAR
    setAvatar(IMAGE_URLS.DEFAULT_AVATAR)
  }

  // 显示客服二维码或反馈弹窗 - 根据语言区分
  const showCustomerServiceQR = () => {
    if (currentLanguage === 'zh') {
      setShowCustomerService(true)
    } else {
      setShowFeedbackModal(true)
    }
  }

  // 取消订阅 (保留原方法)
  const cancelSub = () => {
    Modal.confirm({
      title: t('subscription.confirmTitle'),
      zIndex: 4000,
      okButtonProps: { style: { backgroundColor: '#D7813F', borderColor: '#D7813F' } },
      content: t('subscription.confirmCancel').replace('{expireTime}', subStatusDetail?.expireAt || ''),
      onOk: async () => {
        const res = await xAiApi.cancelSubscription();
        message.success(t('subscription.cancelSuccess'));
        onClose()
      },
      onCancel: () => {
        // on cancel
      }
    });
  };

  return (
    <>
      <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* 背景遮罩 */}
      <div
        className="absolute inset-0 bg-black bg-opacity-30"
        onClick={onClose}
      />

      {/* 弹框内容 */}
      <div className="relative bg-white rounded-2xl shadow-2xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* 关闭按钮 */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 z-10 w-8 h-8 flex items-center justify-center rounded-full bg-gray-100 hover:bg-gray-200 transition-colors"
        >
          <span className="text-gray-500 text-lg">×</span>
        </button>

        {/* 整体内容区域 - 统一渐变背景 */}
        <div className="rounded-t-2xl rounded-b-2xl" style={{
          background: 'linear-gradient(to bottom, #fef9f3, #fefcf8, #fefefe, #ffffff)'
        }}>
          {/* 头部区域 */}
          <div className="px-8 pt-4 pb-4">
            {/* 用户头像 - 左上角 */}
            <div className="flex justify-between items-start mb-4">
              <div className="flex items-center gap-3">
                {userInfo && (
                  <CrownBorder tier={getCurrentAppTier()} className="w-12 h-12">
                    <div className="w-12 h-12 rounded-xl flex items-center justify-center p-1 relative">
                      <div className="w-full h-full bg-gradient-to-br rounded-full flex items-center justify-center">
                          <img
                            src={userInfo.avatar? userInfo.avatar : avatar} alt={username} onError={errorImg}
                            className="w-full h-full object-cover rounded-full"
                          />
                      </div>
                      {/* 订阅状态图标 - 显示在头像左上角 */}
                      {getCurrentAppTier() && getCurrentAppTier() !== 'base' && (
                        <div className="absolute -top-0.5 -left-0.5 bg-white rounded-full p-0.5 shadow-sm">
                          {getCurrentAppTier() === 'pro' && <VIPIcon width={14} height={14} />}
                          {getCurrentAppTier() === 'ultra' && <DiamondCrownIcon width={14} height={14} />}
                        </div>
                      )}
                    </div>
                  </CrownBorder>
                )}
                <div className="text-left">
                  <div className="text-gray-900">{username}</div>
                </div>
              </div>

              {/* 应用信息和描述 - 居中 */}
              <div className="absolute left-1/2 transform -translate-x-1/2 text-center w-96">
                <div className="flex items-center justify-center gap-3 mb-2">
                  <div className="w-12 h-12 flex items-center justify-center">
                    <img src={currentApp.appIcon} alt={currentApp.appName} className="w-12 h-12" />
                  </div>
                  <h2 className="text-2xl font-bold text-gray-900">{currentApp.appName}</h2>
                </div>
              </div>
            </div>
          </div>

          <div className="text-sm text-gray-500 whitespace-pre-line mt-2 w-[500px] mx-auto text-center">
            {t('subscription.desc.'+currentApp.appNameEn)}
          </div>


          {/* 套餐选择区域 */}
          <div className="px-8 pb-8 pt-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {subStatusDetail?.feeTypes?.map((fee) => {
                // 判断是否禁用：当前已订阅非免费套餐时，禁止点击其他套餐
                const isDisabled = (subStatusDetail?.subStatus === 1 && subStatusDetail?.packageType !== '免费') || subStatusDetail?.subStatus === 3;

                return (
                  <div
                    key={fee.type}
                    className={`relative rounded-xl border-2 p-6 transition-all duration-200 ${isDisabled && selectedFee?.type !== fee.type
                        ? 'cursor-not-allowed opacity-50 bg-gray-100 border-gray-200'
                        : `cursor-pointer hover:shadow-lg ${selectedFee?.type === fee.type
                          ? 'border-blue-400 bg-blue-50 shadow-md'
                          : 'border-gray-200 bg-white hover:border-gray-300'
                        }`
                      }`}
                    onClick={() => !isDisabled && handlePlanSelect(fee)}
                  >

                    {/* 套餐名称 */}
                    <div className="text-center">
                      <h4 className="text-lg font-semibold text-gray-900 mb-2">{getPlanTypeName(fee.type)}</h4>
                      <div className="flex flex-col items-center">
                        <span className="text-3xl font-bold text-gray-900">{fee.coinType=='人民币'?'¥':'$'} {fee.feePrice}</span>
                        {fee.oldPrice && fee.oldPrice !== fee.feePrice && (
                          <span className="text-sm text-gray-400 line-through">{t('subscription.standardPrice')}：{fee.coinType=='人民币'?'¥':'$'} {fee.oldPrice}</span>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>

            {/* 底部说明 */}
            <div className="mt-8 text-center">
              {/* 订阅状态显示 */}
              <div className="mt-6">
                <div className="bg-white border border-gray-200 rounded-xl p-4 h-44" >
                  {subStatusDetail?.subStatus === 1 && (
                    // 订阅中
                    (subStatusDetail.packageType === '免费' && selectedFee?.type !== '免费') ? (
                        <PaymentInterface
                          selectedFee={selectedFee}
                          selectedPeriodText={selectedPeriodText}
                          username={username}
                          xAiApi={xAiApi}
                          subscribe={subscribe}
                          paymentLoading={paymentLoading}
                          payUrl={payUrl}
                          showFeedbackModal={showFeedbackModal}
                          setShowFeedbackModal={setShowFeedbackModal}
                        />
                    ) : (
                      <div className="text-center">
                        <div className="flex items-center justify-center gap-2 mb-2">
                          {/* 图标 */}
                          <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                            <span className="text-green-600 text-sm">✓</span>
                          </div>
                          <h3 className="text-lg font-semibold text-gray-900">{t('subscription.subscribing')}</h3>
                        </div>
                        <p className="text-gray-600 mb-2">{t('subscription.subscriptionActive')}</p>
                        {appUser?.expireAt && (
                          <p className="text-sm text-gray-500 mb-4">{t('subscription.expireTime')}{appUser.expireAt}</p>
                        )}
                        { subStatusDetail.packageType !== '免费' && (<button
                            onClick={showCustomerServiceQR}
                            className="px-4 py-2 text-sm text-blue-600 border border-blue-200 rounded-xl hover:bg-blue-50 transition-colors"
                          >
                            {t('subscription.cancelSubscription')}
                          </button>
                        )}
                      </div>
                    )
                  )}

                  {subStatusDetail?.subStatus === 3 && (
                    // 退订中状态
                    <div>
                      <div className="text-center">
                        <div className="flex items-center justify-center gap-2 mb-2">
                          {/* 图标 */}
                          <div className="w-6 h-6 bg-orange-100 rounded-full flex items-center justify-center flex-shrink-0">
                            <span className="text-orange-600 text-sm">⏳</span>
                          </div>
                          <h3 className="text-lg font-semibold text-gray-900">{t('subscription.cancelled')}</h3>
                        </div>
                        <p className="text-gray-600 mb-2">{t('subscription.subscriptionCancelled')}</p>
                        <p className="text-sm text-gray-500">{t('subscription.expireTime')}：{subStatusDetail?.expireAt}</p>
                      </div>
                    </div>
                  )}

                  {(!subStatusDetail?.subStatus || subStatusDetail?.subStatus === 0 || subStatusDetail?.subStatus === 2) && (
                    // 未订阅 或 已过期状态
                    <PaymentInterface
                      selectedFee={selectedFee}
                      selectedPeriodText={selectedPeriodText}
                      username={username}
                      xAiApi={xAiApi}
                      subscribe={subscribe}
                      paymentLoading={paymentLoading}
                      payUrl={payUrl}
                      showFeedbackModal={showFeedbackModal}
                      setShowFeedbackModal={setShowFeedbackModal}
                    />
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      </div>

      {/* 客服二维码Modal */}
      <Modal
        open={showCustomerService}
        onCancel={() => setShowCustomerService(false)}
        footer={null}
        centered
        width={260}
        zIndex={5000}
      >
        <div className="text-center py-2">
          <div className="flex justify-center mb-2">
            <img src={IMAGE_URLS.CUSTOMER_SERVICE_QR} alt="扫码添加小助手" className="w-48 h-48" />
          </div>
        </div>
      </Modal>

      {/* 意见反馈弹窗 */}
      <FeedbackModal
        open={showFeedbackModal}
        onCancel={() => setShowFeedbackModal(false)}
      />
    </>
  )
}

export default Pay
