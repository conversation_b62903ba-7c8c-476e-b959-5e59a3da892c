{"root": ["./src/app.tsx", "./src/main.tsx", "./src/vite-env.d.ts", "./src/api/src/base-request.ts", "./src/api/src/dify-api.ts", "./src/api/src/index.ts", "./src/api/src/xai-api.ts", "./src/api/src/enums/index.ts", "./src/api/src/types/event.ts", "./src/api/src/types/file.ts", "./src/api/src/types/index.ts", "./src/api/src/types/message.ts", "./src/api/src/utils/file-type.ts", "./src/api/src/utils/index.ts", "./src/components/caseexample.tsx", "./src/components/chatdetail.tsx", "./src/components/conversationlist.tsx", "./src/components/conversationpage.tsx", "./src/components/feedbackmodal.tsx", "./src/components/floatingbuttons.tsx", "./src/components/header.tsx", "./src/components/home.tsx", "./src/components/i18nroutewrapper.tsx", "./src/components/languageswitcher.tsx", "./src/components/loginmanager.tsx", "./src/components/maincontent.tsx", "./src/components/pagetitle.tsx", "./src/components/sidebar.tsx", "./src/components/sidebarbackup.tsx", "./src/components/airesponserenderer/index.tsx", "./src/components/mermaiddiagram/index.tsx", "./src/components/simplemermaid/index.tsx", "./src/components/about/aboutusmodal.tsx", "./src/components/chat/assistantmessage.tsx", "./src/components/chat/chatdetailheader.tsx", "./src/components/chat/chatfileupload.tsx", "./src/components/chat/chatheader.tsx", "./src/components/chat/chatinput.tsx", "./src/components/chat/loadingmessage.tsx", "./src/components/chat/messagelist.tsx", "./src/components/chat/mobilelayouttest.tsx", "./src/components/chat/renderoptimizationtest.tsx", "./src/components/chat/tableofcontents.tsx", "./src/components/chat/usermessage.tsx", "./src/components/faq/faqmodal.tsx", "./src/components/file-upload/fileicon.tsx", "./src/components/file-upload/fileupload.tsx", "./src/components/file-upload/fileuploadstatus.tsx", "./src/components/file-upload/usefileupload.ts", "./src/components/icons/icons.tsx", "./src/components/login/login.tsx", "./src/components/login/signup.tsx", "./src/components/mobile/mobilesearchresults.tsx", "./src/components/pay/pay.tsx", "./src/components/pay/paymobile.tsx", "./src/components/subscription-status/crownborder.tsx", "./src/components/tool-tip/tooltip.tsx", "./src/contexts/i18ncontext.tsx", "./src/hooks/useloginstatus.ts", "./src/hooks/usepagetitle.ts", "./src/hooks/userenderperformance.ts", "./src/i18n/helpers.ts", "./src/i18n/hooks.ts", "./src/i18n/index.ts", "./src/i18n/simple-hooks.ts", "./src/i18n/simple.ts", "./src/i18n/utils.ts", "./src/types/i18n.ts", "./src/utils/apiconfig.ts", "./src/utils/envconfig.ts", "./src/utils/feedbackconfig.ts", "./src/utils/mermaidutils.ts", "./src/utils/performancemonitor.ts", "./vite.config.ts"], "version": "5.6.3"}