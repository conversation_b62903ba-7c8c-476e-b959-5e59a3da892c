/**
 * 简化版 Mermaid 组件
 * 专门用于 AiResponseRenderer，确保稳定性
 */

import React, { useEffect, useState } from 'react';
import mermaid from 'mermaid';

interface SimpleMermaidProps {
  code: string;
  className?: string;
}

// 全局初始化状态
let mermaidInitialized = false;

// 处理 Mermaid 代码中的 Markdown 格式和文本换行
const processMermaidMarkdown = (code: string): string => {
  let processedCode = code;

  // 处理粗体 **text** -> <b>text</b>
  processedCode = processedCode.replace(/\*\*(.*?)\*\*/g, '<b>$1</b>');

  // 处理斜体 *text* -> <i>text</i>
  processedCode = processedCode.replace(/\*(.*?)\*/g, '<i>$1</i>');

  // 处理代码 `code` -> <code>code</code>
  processedCode = processedCode.replace(/`([^`]+)`/g, '<code>$1</code>');

  // 处理删除线 ~~text~~ -> <s>text</s>
  processedCode = processedCode.replace(/~~(.*?)~~/g, '<s>$1</s>');

  // 处理长文本换行 - 只在真正需要时换行，保持题号和题目在一起
  processedCode = processedCode.replace(/\[([^\]]{40,})\]/g, (_, content) => {
    const maxLength = 30; // 每行最大字符数，更宽松

    // 检查是否包含题号模式（如：1.1、2.3、A1、B2等）
    const titlePattern = /^(\d+\.?\d*\.?\s*|[A-Z]\d*\.?\s*)/;
    const titleMatch = content.match(titlePattern);

    if (titleMatch) {
      // 有题号的情况：保持题号和第一部分内容在一起
      const titlePart = titleMatch[0];
      const restContent = content.substring(titlePart.length);

      if (content.length <= maxLength * 1.5) {
        return `[${content}]`; // 不需要换行，给更多容忍度
      }

      // 找到第一个合适的断点（空格、标点符号等）
      let firstLineEnd = maxLength - titlePart.length;
      const breakPoints = [' ', '，', '。', '：', '；', '、'];

      // 如果剩余内容不长，就不要强制换行
      if (restContent.length <= maxLength * 0.8) {
        return `[${content}]`; // 保持在一行
      }

      for (let i = firstLineEnd; i >= titlePart.length / 2; i--) {
        if (breakPoints.includes(restContent[i])) {
          firstLineEnd = i + 1;
          break;
        }
      }

      const firstLine = titlePart + restContent.substring(0, firstLineEnd);
      const remainingText = restContent.substring(firstLineEnd);

      if (remainingText.length === 0) {
        return `[${firstLine}]`;
      }

      // 处理剩余文本
      const lines = [firstLine];
      let currentPos = 0;

      while (currentPos < remainingText.length) {
        let lineEnd = Math.min(currentPos + maxLength, remainingText.length);

        // 寻找合适的断点
        if (lineEnd < remainingText.length) {
          for (let i = lineEnd; i >= currentPos + maxLength / 2; i--) {
            if (breakPoints.includes(remainingText[i])) {
              lineEnd = i + 1;
              break;
            }
          }
        }

        lines.push(remainingText.substring(currentPos, lineEnd).trim());
        currentPos = lineEnd;
      }

      return `[${lines.filter(line => line.length > 0).join('<br/>')}]`;
    } else {
      // 没有题号的普通文本处理
      const words = content.split(/(\s+|，|。|：|；|、)/);
      let lines = [];
      let currentLine = '';

      for (const word of words) {
        if ((currentLine + word).length > maxLength && currentLine.length > 0) {
          lines.push(currentLine.trim());
          currentLine = word;
        } else {
          currentLine += word;
        }
      }
      if (currentLine.trim()) {
        lines.push(currentLine.trim());
      }

      return `[${lines.join('<br/>')}]`;
    }
  });

  return processedCode;
};

const SimpleMermaid: React.FC<SimpleMermaidProps> = ({ code, className = '' }) => {
  const [svg, setSvg] = useState<string>('');
  const [error, setError] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);

  useEffect(() => {
    const render = async () => {
      setLoading(true);
      setError('');
      setSvg('');

      try {
        // 初始化 Mermaid（只初始化一次）
        if (!mermaidInitialized) {
          mermaid.initialize({
            startOnLoad: false,
            theme: 'default',
            securityLevel: 'loose',
            fontFamily: 'inherit',
            htmlLabels: true, // 启用 HTML 标签支持
            flowchart: {
              htmlLabels: true
            }
          });
          mermaidInitialized = true;
        }

        // 验证代码
        if (!code || !code.trim()) {
          throw new Error('Mermaid 代码为空');
        }

        // 清理代码
        let cleanCode = code.trim();
        cleanCode = cleanCode.replace(/(\]\s*)([A-Z]\d*\[)/g, '$1\n$2');

        // 处理 Markdown 格式
        cleanCode = processMermaidMarkdown(cleanCode);

        // 生成唯一ID
        const id = `simple-mermaid-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
        
        // 渲染
        const { svg } = await mermaid.render(id, cleanCode);
        setSvg(svg);
      } catch (err: any) {
        setError(err.message || '渲染失败');
      } finally {
        setLoading(false);
      }
    };

    render();
  }, [code]);

  // 弹窗组件
  const Modal = () => {
    const [scale, setScale] = useState(1.5); // 默认放大到 150%
    const [position, setPosition] = useState({ x: 0, y: 0 });
    const [isDragging, setIsDragging] = useState(false);
    const [dragStart, setDragStart] = useState({ x: 0, y: 0 });

    if (!isModalOpen) return null;

    const handleZoomIn = () => setScale(prev => Math.min(prev + 0.25, 5)); // 最大 500%
    const handleZoomOut = () => setScale(prev => Math.max(prev - 0.25, 0.3)); // 最小 30%

    // 键盘快捷键支持
    React.useEffect(() => {
      const handleKeyDown = (e: KeyboardEvent) => {
        if (e.key === 'Escape') {
          setIsModalOpen(false);
        } else if (e.ctrlKey || e.metaKey) {
          switch (e.key) {
            case '=':
            case '+':
              e.preventDefault();
              handleZoomIn();
              break;
            case '-':
              e.preventDefault();
              handleZoomOut();
              break;
          }
        }
      };

      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }, []);

    const handleMouseDown = (e: React.MouseEvent) => {
      setIsDragging(true);
      setDragStart({
        x: e.clientX - position.x,
        y: e.clientY - position.y
      });
    };

    const handleMouseMove = (e: React.MouseEvent) => {
      if (isDragging) {
        setPosition({
          x: e.clientX - dragStart.x,
          y: e.clientY - dragStart.y
        });
      }
    };

    const handleMouseUp = () => setIsDragging(false);

    return (
      <div className="fixed inset-0 z-[9999] bg-black bg-opacity-90">
        {/* 工具栏 - 固定在顶部 */}
        <div className="absolute top-0 left-0 right-0 z-[10000] bg-white border-b border-gray-200 shadow-lg">
          <div className="flex items-center justify-between p-4">
            <h3 className="text-xl font-semibold text-gray-900">Mermaid 图表</h3>
            <div className="flex items-center space-x-3">
              {/* 缩放控制 */}
              <div className="flex items-center space-x-2 bg-gray-100 rounded-lg p-1">
                <button
                  onClick={handleZoomOut}
                  className="p-2 text-gray-600 hover:text-gray-800 hover:bg-white rounded transition-colors"
                  title="缩小 (Ctrl + -)"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                  </svg>
                </button>

                <span className="text-sm font-medium text-gray-700 min-w-[70px] text-center px-2">
                  {Math.round(scale * 100)}%
                </span>

                <button
                  onClick={handleZoomIn}
                  className="p-2 text-gray-600 hover:text-gray-800 hover:bg-white rounded transition-colors"
                  title="放大 (Ctrl + +)"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                </button>
              </div>

              <div className="w-px h-8 bg-gray-300" />

              {/* 下载按钮 */}
              <button
                onClick={() => {
                  const blob = new Blob([svg], { type: 'image/svg+xml' });
                  const url = URL.createObjectURL(blob);
                  const a = document.createElement('a');
                  a.href = url;
                  a.download = 'mermaid-diagram.svg';
                  document.body.appendChild(a);
                  a.click();
                  document.body.removeChild(a);
                  URL.revokeObjectURL(url);
                }}
                className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded transition-colors"
                title="下载 SVG"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </button>

              {/* 关闭按钮 */}
              <button
                onClick={() => setIsModalOpen(false)}
                className="p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded transition-colors"
                title="关闭 (ESC)"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>
        </div>

        {/* 图表容器 - 占满整个屏幕 */}
        <div
          className="absolute inset-0 pt-20 overflow-hidden bg-gray-100"
          onClick={(e) => {
            // 点击空白区域关闭弹窗
            if (e.target === e.currentTarget) {
              setIsModalOpen(false);
            }
          }}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp}
        >
          <div
            className={`w-full h-full flex items-center justify-center ${isDragging ? 'cursor-grabbing' : 'cursor-grab'}`}
            style={{
              transform: `translate(${position.x}px, ${position.y}px) scale(${scale})`,
              transformOrigin: 'center center'
            }}
            onMouseDown={handleMouseDown}
          >
            <div
              className="bg-white p-8 rounded-lg shadow-2xl max-w-none"
              style={{
                minWidth: '60vw',
                minHeight: '40vh'
              }}
              dangerouslySetInnerHTML={{ __html: svg }}
            />
          </div>
        </div>

        {/* 底部提示信息 */}
        <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 z-[10000]">
          <div className="bg-black bg-opacity-80 text-white text-sm px-6 py-3 rounded-lg backdrop-blur-sm">
            <div className="flex items-center space-x-4 text-center">
              <span>🖱️ 拖拽移动</span>
              <span>•</span>
              <span>🔍 +/- 缩放</span>
              <span>•</span>
              <span>ESC 关闭</span>
            </div>
          </div>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className={`p-4 text-center border border-gray-200 rounded-lg bg-gray-50 ${className}`}>
        <div className="flex items-center justify-center space-x-2">
          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500"></div>
          <span className="text-gray-600">正在渲染图表...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`border border-red-200 bg-red-50 rounded-lg p-4 ${className}`}>
        <div className="flex items-start space-x-2">
          <svg className="w-5 h-5 text-red-400 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
          </svg>
          <div className="flex-1">
            <h3 className="text-sm font-medium text-red-800">图表渲染失败</h3>
            <p className="mt-1 text-sm text-red-700">{error}</p>
            <details className="mt-2">
              <summary className="text-sm text-red-600 cursor-pointer hover:text-red-800">
                查看源代码
              </summary>
              <pre className="mt-2 text-xs bg-red-100 p-2 rounded border overflow-x-auto">
                {code}
              </pre>
            </details>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className={`border border-gray-200 rounded-lg p-4 bg-white overflow-x-auto relative group ${className}`}>
        <div 
          className="cursor-pointer"
          onClick={() => setIsModalOpen(true)}
          dangerouslySetInnerHTML={{ __html: svg }}
        />
        
        {/* 悬浮工具栏 */}
        <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
          <button
            onClick={() => setIsModalOpen(true)}
            className="p-2 bg-white rounded-lg shadow-lg border border-gray-200 text-gray-500 hover:text-gray-700"
            title="点击放大查看"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
            </svg>
          </button>
        </div>
        
        {/* 点击提示 */}
        <div className="absolute bottom-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
          <div className="bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
            点击放大查看
          </div>
        </div>
      </div>
      
      <Modal />
    </>
  );
};

export default SimpleMermaid;
