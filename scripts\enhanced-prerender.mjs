import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 预渲染的路由配置
const routes = [
  {
    path: '/',
    lang: 'zh',
    appName: 'novax-base',
    title: '梅斯小智-梅斯医学AI智能体',
    description: 'MedSci(梅斯医学)旗下梅斯小智是医药领域专属的AI智能体，包括医药写作，翻译，患者诊疗，疑难疾病诊断治疗，医药策略，在线智能医疗，中医大师，药物相互作用，心理咨询，体检报告解读等智能体。',
    keywords: '梅斯小智,AI智能体,AI医学写作,AI医学工具,AI医学问答,人工智能,医学AI,科研助手',
    image: 'https://img.medsci.cn/202412/8a5663cb16a84c45b9c8c5db4eda0075-6pyGtTcj0asd.png'
  },
  {
    path: '/zh/novax-base',
    lang: 'zh',
    appName: 'novax-base',
    title: 'NovaX Base - AI智能助手',
    description: 'NovaX Base - 您的灵感引擎，构建前瞻性研究蓝图。专业的AI科研助手，帮助您进行学术研究、论文写作和数据分析。',
    keywords: '梅斯小智,AI智能体,NovaX,科研AI,学术写作,论文助手,研究分析,数据分析,科研工具',
    image: 'https://ai.medon.com.cn/dev-api/admin-api/infra/file/4/get/6c1534c1fdbea7aeedad03bd65aff86bc2dccb2347f8889745efc725538c7f36.png'
  },
  {
    path: '/zh/novax-pro',
    lang: 'zh',
    appName: 'novax-pro',
    title: 'NovaX Pro - AI智能助手',
    description: 'NovaX Pro - 专业版科研AI助手，提供更强大的研究分析能力和专业的学术写作支持。',
    keywords: '梅斯小智,AI智能体,NovaX Pro,专业科研,高级AI,学术研究,专业写作,科研分析',
    image: 'https://ai.medon.com.cn/dev-api/admin-api/infra/file/4/get/6c1534c1fdbea7aeedad03bd65aff86bc2dccb2347f8889745efc725538c7f36.png'
  },
  {
    path: '/zh/elavax-base',
    lang: 'zh',
    appName: 'elavax-base',
    title: 'ElaVaX Base - AI智能助手',
    description: 'ElaVaX Base - 精准剖析，深度洞察，赋能您的卓越创见。专业的医学AI分析工具。',
    keywords: '梅斯小智,AI智能体,ElaVaX,医学AI,医学分析,诊断助手,医疗工具,临床决策',
    image: 'https://ai.medon.com.cn/dev-api/admin-api/infra/file/4/get/fbf91c551baa82e54b413ccec5f166d2f810db5f8fb928919f0d9f8a344d5664.png'
  },
  {
    path: '/zh/elavax-pro',
    lang: 'zh',
    appName: 'elavax-pro',
    title: 'ElaVaX Pro - AI智能助手',
    description: 'ElaVaX Pro - 专业版医学AI分析工具，提供更深度的医学数据分析和诊断支持。',
    keywords: '梅斯小智,AI智能体,ElaVaX Pro,专业医学AI,高级诊断,医学数据分析,临床支持',
    image: 'https://ai.medon.com.cn/dev-api/admin-api/infra/file/4/get/fbf91c551baa82e54b413ccec5f166d2f810db5f8fb928919f0d9f8a344d5664.png'
  },
  {
    path: '/zh/datascore-base',
    lang: 'zh',
    appName: 'datascore-base',
    title: 'DataScore Base - AI智能助手',
    description: 'DataScore Base - 专业的数据分析AI工具，提供智能的数据处理和分析服务。',
    keywords: '梅斯小智,AI智能体,DataScore,数据分析,AI数据工具,智能分析',
    image: 'https://img.medsci.cn/202412/8a5663cb16a84c45b9c8c5db4eda0075-6pyGtTcj0asd.png'
  },
  // 英文版本
  {
    path: '/en/novax-base',
    lang: 'en',
    appName: 'novax-base',
    title: 'NovaX Base - AI Assistant',
    description: 'NovaX Base - Your inspiration engine for building forward-looking research blueprints. Professional AI research assistant to help you with academic research, paper writing and data analysis.',
    keywords: 'MedSci AI,AI intelligent agents,NovaX,research AI,academic writing,paper assistant,research analysis,data analysis,research tools',
    image: 'https://ai.medon.com.cn/dev-api/admin-api/infra/file/4/get/6c1534c1fdbea7aeedad03bd65aff86bc2dccb2347f8889745efc725538c7f36.png'
  },
  {
    path: '/en/novax-pro',
    lang: 'en',
    appName: 'novax-pro',
    title: 'NovaX Pro - AI Assistant',
    description: 'NovaX Pro - Professional research AI assistant providing more powerful research analysis capabilities and professional academic writing support.',
    keywords: 'MedSci AI,AI intelligent agents,NovaX Pro,professional research,advanced AI,academic research,professional writing,research analysis',
    image: 'https://ai.medon.com.cn/dev-api/admin-api/infra/file/4/get/6c1534c1fdbea7aeedad03bd65aff86bc2dccb2347f8889745efc725538c7f36.png'
  },
  {
    path: '/en/elavax-base',
    lang: 'en',
    appName: 'elavax-base',
    title: 'ElaVaX Base - AI Assistant',
    description: 'ElaVaX Base - Precise analysis, deep insights, empowering your excellent ideas. Professional medical AI analysis tool.',
    keywords: 'MedSci AI,AI intelligent agents,ElaVaX,medical AI,medical analysis,diagnostic assistant,medical tools,clinical decision',
    image: 'https://ai.medon.com.cn/dev-api/admin-api/infra/file/4/get/fbf91c551baa82e54b413ccec5f166d2f810db5f8fb928919f0d9f8a344d5664.png'
  },
  {
    path: '/en/elavax-pro',
    lang: 'en',
    appName: 'elavax-pro',
    title: 'ElaVaX Pro - AI Assistant',
    description: 'ElaVaX Pro - Professional medical AI analysis tool providing deeper medical data analysis and diagnostic support.',
    keywords: 'MedSci AI,AI intelligent agents,ElaVaX Pro,professional medical AI,advanced diagnosis,medical data analysis,clinical support',
    image: 'https://ai.medon.com.cn/dev-api/admin-api/infra/file/4/get/fbf91c551baa82e54b413ccec5f166d2f810db5f8fb928919f0d9f8a344d5664.png'
  }
]

// 增强的HTML模板，包含完整的SEO标签
const htmlTemplate = `<!DOCTYPE html>
<html lang="{{LANG}}">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  
  <!-- 基础SEO标签 -->
  <title>{{TITLE}}</title>
  <meta name="description" content="{{DESCRIPTION}}" />
  <meta name="keywords" content="{{KEYWORDS}}" />
  <meta name="robots" content="index, follow" />
  <meta name="author" content="MedSci" />
  
  <!-- Open Graph 标签 -->
  <meta property="og:type" content="website">
  <meta property="og:title" content="{{TITLE}}">
  <meta property="og:description" content="{{DESCRIPTION}}">
  <meta property="og:url" content="{{URL}}">
  <meta property="og:image" content="{{IMAGE}}">
  <meta property="og:site_name" content="梅斯小智">
  <meta property="og:locale" content="{{LOCALE}}">
  
  <!-- Twitter Card 标签 -->
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="{{TITLE}}">
  <meta name="twitter:description" content="{{DESCRIPTION}}">
  <meta name="twitter:image" content="{{IMAGE}}">
  
  <!-- 移动端优化 -->
  <meta name="format-detection" content="telephone=no">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
  
  <!-- 图标和链接 -->
  <link rel="icon" type="image/x-icon" href="https://static.medsci.cn/product/medsci-site/portal/favicon.ico" />
  <link rel="canonical" href="{{URL}}" />
  
  <!-- 预加载关键资源 -->
  <link rel="preconnect" href="https://ai.medon.com.cn">
  <link rel="preconnect" href="https://img.medsci.cn">
  
  <!-- 构建后的资源文件 -->
  {{ASSETS}}
</head>
<body>
  <div id="root">{{PRERENDERED_CONTENT}}</div>
  
  <!-- 预渲染标记 -->
  <script>
    window.__PRERENDERED__ = true;
    window.__PRERENDER_DATA__ = {
      route: '{{ROUTE}}',
      lang: '{{LANG}}',
      appName: '{{APP_NAME}}',
      timestamp: '{{TIMESTAMP}}'
    };
  </script>
  
  <!-- 分析脚本 -->
  <script src="./js/analytics.js"></script>
</body>
<script src="./js/jquery-latest.min.js"></script>
<script src="./js/iframe-test-v1.js" defer></script>
</html>`

// 生成预渲染内容的函数
function generatePrerenderedContent(route) {
  const { path, lang, appName, title, description } = route
  
  // 这里可以根据需要生成更复杂的预渲染内容
  // 目前返回一个基础的加载状态
  return `
    <div class="prerender-loading" style="
      display: flex; 
      justify-content: center; 
      align-items: center; 
      height: 100vh; 
      font-family: system-ui, -apple-system, sans-serif;
      background-color: #f8fafc;
    ">
      <div style="text-align: center;">
        <div style="
          width: 40px; 
          height: 40px; 
          border: 3px solid #e2e8f0; 
          border-top: 3px solid #3b82f6; 
          border-radius: 50%; 
          animation: spin 1s linear infinite; 
          margin: 0 auto 16px;
        "></div>
        <div style="color: #64748b; font-size: 14px;">
          ${lang === 'zh' ? '正在加载' : 'Loading'} ${title}...
        </div>
      </div>
    </div>
    <style>
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
  `
}

// 读取构建后的资源文件
function getAssetTags(distDir) {
  const indexHtmlPath = path.join(distDir, 'index.html')
  
  if (!fs.existsSync(indexHtmlPath)) {
    console.warn('构建后的 index.html 不存在，使用默认资源标签')
    return `
      <script type="module" crossorigin src="./assets/index.js"></script>
      <link rel="stylesheet" crossorigin href="./assets/index.css">
    `
  }
  
  const indexHtml = fs.readFileSync(indexHtmlPath, 'utf-8')
  
  // 提取 script 和 link 标签
  const scriptMatch = indexHtml.match(/<script[^>]*src="[^"]*"[^>]*><\/script>/g)
  const linkMatch = indexHtml.match(/<link[^>]*rel="stylesheet"[^>]*>/g)
  
  const scripts = scriptMatch ? scriptMatch.join('\n  ') : ''
  const links = linkMatch ? linkMatch.join('\n  ') : ''
  
  return `${links}\n  ${scripts}`
}

// 生成预渲染文件
async function generateEnhancedPrerenderedFiles() {
  const distDir = path.resolve(__dirname, '../dist')
  
  // 确保dist目录存在
  if (!fs.existsSync(distDir)) {
    fs.mkdirSync(distDir, { recursive: true })
  }
  
  // 获取资源标签
  const assetTags = getAssetTags(distDir)
  
  for (const route of routes) {
    const { path: routePath, lang, appName, title, description, keywords, image } = route
    
    console.log(`正在生成增强预渲染: ${routePath}`)
    
    // 生成预渲染内容
    const prerenderedContent = generatePrerenderedContent(route)
    
    // 生成完整的HTML
    const html = htmlTemplate
      .replace(/{{LANG}}/g, lang)
      .replace(/{{TITLE}}/g, title)
      .replace(/{{DESCRIPTION}}/g, description)
      .replace(/{{KEYWORDS}}/g, keywords)
      .replace(/{{IMAGE}}/g, image)
      .replace(/{{URL}}/g, `https://ai.medsci.cn${routePath}`)
      .replace(/{{LOCALE}}/g, lang === 'zh' ? 'zh_CN' : 'en_US')
      .replace(/{{ROUTE}}/g, routePath)
      .replace(/{{APP_NAME}}/g, appName)
      .replace(/{{TIMESTAMP}}/g, new Date().toISOString())
      .replace(/{{ASSETS}}/g, assetTags)
      .replace(/{{PRERENDERED_CONTENT}}/g, prerenderedContent)
    
    // 确定文件路径
    let filePath
    if (routePath === '/') {
      filePath = path.join(distDir, 'index.html')
    } else {
      const routeDir = path.join(distDir, routePath)
      fs.mkdirSync(routeDir, { recursive: true })
      filePath = path.join(routeDir, 'index.html')
    }
    
    // 写入文件
    fs.writeFileSync(filePath, html)
    console.log(`✅ 生成成功: ${filePath}`)
  }
  
  console.log(`\n🎉 增强预渲染完成！生成了 ${routes.length} 个页面`)
  console.log('📊 SEO优化特性:')
  console.log('  ✅ 完整的TDK标签')
  console.log('  ✅ Open Graph支持')
  console.log('  ✅ Twitter Card支持')
  console.log('  ✅ 移动端优化')
  console.log('  ✅ 预加载优化')
  console.log('  ✅ 结构化数据准备')
}

// 执行增强预渲染
generateEnhancedPrerenderedFiles().catch(error => {
  console.error('❌ 增强预渲染失败:', error)
  process.exit(1)
})
