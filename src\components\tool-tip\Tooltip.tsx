import React, { useState, useRef, useEffect } from 'react'
import './Tooltip.css'

interface TooltipProps {
  content: React.ReactNode
  children: React.ReactNode
  position?: 'top' | 'bottom' | 'left' | 'right'
  delay?: number
  className?: string
}

declare global {
  namespace NodeJS {
    interface Timeout {}
  }
}

const Tooltip: React.FC<TooltipProps> = ({ 
  content, 
  children, 
  position = 'top', 
  delay = 500,
  className = ''
}) => {
  const [isVisible, setIsVisible] = useState(false)
  const [actualPosition, setActualPosition] = useState(position)
  const timeoutRef = useRef<NodeJS.Timeout | undefined>()
  const tooltipRef = useRef<HTMLDivElement>(null)
  const triggerRef = useRef<HTMLDivElement>(null)

  const showTooltip = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
    timeoutRef.current = setTimeout(() => {
      setIsVisible(true)
      adjustPosition()
    }, delay)
  }

  const hideTooltip = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
    setIsVisible(false)
  }

  const adjustPosition = () => {
    if (!tooltipRef.current || !triggerRef.current) return

    const tooltip = tooltipRef.current
    const trigger = triggerRef.current
    const rect = trigger.getBoundingClientRect()
    const tooltipRect = tooltip.getBoundingClientRect()
    const viewport = {
      width: window.innerWidth,
      height: window.innerHeight
    }

    let newPosition = position

    // 检查是否需要调整位置
    switch (position) {
      case 'top':
        if (rect.top - tooltipRect.height < 10) {
          newPosition = 'bottom'
        }
        break
      case 'bottom':
        if (rect.bottom + tooltipRect.height > viewport.height - 10) {
          newPosition = 'top'
        }
        break
      case 'left':
        if (rect.left - tooltipRect.width < 10) {
          newPosition = 'right'
        }
        break
      case 'right':
        if (rect.right + tooltipRect.width > viewport.width - 10) {
          newPosition = 'left'
        }
        break
    }

    setActualPosition(newPosition)
  }

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [])

  const getPositionClasses = () => {
    const baseClasses = 'tooltip-content absolute z-50 px-3 py-2.5 text-sm font-medium text-gray-700 rounded-lg transition-all duration-200'

    switch (actualPosition) {
      case 'top':
        return `${baseClasses} bottom-full left-1/2 transform -translate-x-1/2 mb-2`
      case 'bottom':
        return `${baseClasses} top-full left-1/2 transform -translate-x-1/2 mt-2`
      case 'left':
        return `${baseClasses} right-full top-1/2 transform -translate-y-1/2 mr-2`
      case 'right':
        return `${baseClasses} left-full top-1/2 transform -translate-y-1/2 ml-2`
      default:
        return baseClasses
    }
  }

  const getArrowClasses = () => {
    const baseArrowClasses = 'tooltip-arrow absolute w-2 h-2 bg-white border-gray-200 transform rotate-45'

    switch (actualPosition) {
      case 'top':
        return `${baseArrowClasses} border-b border-r top-full left-1/2 -translate-x-1/2 -mt-1`
      case 'bottom':
        return `${baseArrowClasses} border-t border-l bottom-full left-1/2 -translate-x-1/2 -mb-1`
      case 'left':
        return `${baseArrowClasses} border-t border-r left-full top-1/2 -translate-y-1/2 -ml-1`
      case 'right':
        return `${baseArrowClasses} border-b border-l right-full top-1/2 -translate-y-1/2 -mr-1`
      default:
        return baseArrowClasses
    }
  }

  return (
    <div
      className={`tooltip-container ${className}`}
      onMouseEnter={showTooltip}
      onMouseLeave={hideTooltip}
      onFocus={showTooltip}
      onBlur={hideTooltip}
      ref={triggerRef}
    >
      {children}
      
      {isVisible && (
        <div
          ref={tooltipRef}
          className={getPositionClasses()}
          style={{
            opacity: isVisible ? 1 : 0,
            pointerEvents: 'none'
          }}
        >
          {content}
          <div className={getArrowClasses()}></div>
        </div>
      )}
    </div>
  )
}

export default Tooltip
