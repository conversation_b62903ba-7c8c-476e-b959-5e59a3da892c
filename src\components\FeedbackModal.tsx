import React, { useState, useEffect, useRef } from 'react'
import { Modal, Form, Input, Button, message, Image } from 'antd'
import { DeleteOutlined } from '@ant-design/icons'
import { useSimpleTranslation, useI18nRouter } from '../i18n/simple-hooks'
import { useLoginStatus } from '../hooks/useLoginStatus'
import { XAiApi, FeedbackData, FeedbackAccessory } from '../api/src/xai-api'
import { IDifyApiOptions } from '../api/src/dify-api'
import FileUpload, { FileUploadRef } from './file-upload/FileUpload'
import { getFeedbackProjectId } from '../utils/feedbackConfig'
import { getApiConfig } from '../utils/apiConfig'
import { extractAppNameFromPath, extractSessionIdFromPath } from '../i18n/utils'
import { getEnvConfig } from '../utils/envConfig'

const { TextArea } = Input

interface FeedbackModalProps {
  open: boolean
  onCancel: () => void
}

// 错误信息接口
interface ConsoleError {
  message: string
  source?: string
  line?: number
  column?: number
  timestamp: number
  type: 'error' | 'unhandledrejection'
}

const FeedbackModal: React.FC<FeedbackModalProps> = ({ open, onCancel }) => {
  const { t, currentLanguage } = useSimpleTranslation()
  const [form] = Form.useForm()
  const { userInfo } = useLoginStatus()
  const [submitting, setSubmitting] = useState(false)
  const [uploading, setUploading] = useState(false)
  const [uploadedImages, setUploadedImages] = useState<FeedbackAccessory[]>([])
  const fileUploadRef = useRef<FileUploadRef>(null)
  const consoleErrors = useRef<ConsoleError[]>([])
  const errorListenerAdded = useRef(false)

  // 获取当前应用信息和会话ID
  const currentAppName = extractAppNameFromPath(window.location.pathname)
  const currentSessionId = extractSessionIdFromPath(window.location.pathname)

  // 获取默认邮箱
  const getDefaultEmail = () => {
    const envConfig = getEnvConfig()
    return envConfig.defaultEmail
  }

  // 收集控制台错误
  useEffect(() => {
    if (errorListenerAdded.current) return

    const handleError = (event: ErrorEvent) => {
      consoleErrors.current.push({
        message: event.message,
        source: event.filename,
        line: event.lineno,
        column: event.colno,
        timestamp: Date.now(),
        type: 'error'
      })
    }

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      consoleErrors.current.push({
        message: String(event.reason),
        timestamp: Date.now(),
        type: 'unhandledrejection'
      })
    }

    window.addEventListener('error', handleError)
    window.addEventListener('unhandledrejection', handleUnhandledRejection)
    errorListenerAdded.current = true

    return () => {
      window.removeEventListener('error', handleError)
      window.removeEventListener('unhandledrejection', handleUnhandledRejection)
    }
  }, [])

  // 初始化API实例
  const apiConfig = getApiConfig(userInfo)
  const xaiApi = new XAiApi({
    user: apiConfig.user,
    apiBase: apiConfig.apiBase,
    medxyToken: apiConfig.medxyToken
  })

  // 图片上传处理（支持多文件）
  const handleImageUpload = async (files: File[]) => {
    // 检查文件数量限制（包括已上传和当前选择）
    const totalFiles = uploadedImages.length + files.length
    if (totalFiles > 3) {
      message.error(t('feedback.maxFilesReached'))
      return
    }
    
    // 检查文件大小
    const maxSize = 10 * 1024 * 1024 // 10MB
    const oversizedFiles = files.filter((f: File) => f.size > maxSize)
    if (oversizedFiles.length > 0) {
      message.error(t('feedback.fileTooLarge'))
      return
    }
    
    setUploading(true)
    
    try {
      // 并发上传所有文件（使用xai-api）
      const uploadPromises = files.map((currentFile: File) => {
        return xaiApi.uploadImage(currentFile)
      })
      
      const uploadResults = await Promise.all(uploadPromises)
      setUploadedImages(prev => [...prev, ...uploadResults])
      message.success(t('feedback.uploadSuccess').replace('{count}', files.length.toString()))
    } catch (error) {
      console.error('图片上传失败:', error)
      message.error(t('feedback.uploadFailed'))
    } finally {
      setUploading(false)
    }
  }

  // 删除上传的图片
  const handleImageRemove = (url: string) => {
    setUploadedImages(prev => prev.filter(img => img.url !== url))
  }

  // 格式化反馈内容
  const formatFeedbackContent = (userContent: string) => {
    // 收集系统信息（使用现代浏览器API）
    const systemInfo = [
      `浏览器信息: ${navigator.userAgent}`,
      `语言设置: ${navigator.language}`,
      `屏幕分辨率: ${window.screen.width}x${window.screen.height}`,
    ]

    // 页面和应用信息
    const pageInfo = [
      `页面路径: ${window.location.pathname}`,
      `当前应用: ${currentAppName}`,
    ]

    // 如果在会话页面，添加会话ID信息
    if (currentSessionId && currentSessionId !== 'new' && currentSessionId !== 'chat') {
      pageInfo.push(`会话ID: ${currentSessionId}`)
    }

    // 添加控制台错误信息
    if (consoleErrors.current.length > 0) {
      const errorMessages = consoleErrors.current
        .map(err => `${err.type}: ${err.message}${err.source ? ` at ${err.source}:${err.line}:${err.column}` : ''}`)
        .join('\n')
      systemInfo.push(`console err: ${errorMessages}`)
    } else {
      systemInfo.push('console err: 无错误')
    }

    // 用户信息
    const userInfoData = [
      `用户ID: ${userInfo?.userId || 'unknown'}`,
    ]

    // 组合所有信息
    const allInfo = [
      `用户输入内容：${userContent}`,
      '',
      `用户信息：`,
      ...userInfoData,
      '',
      `系统信息：`,
      ...systemInfo,
      '',
      `页面信息：`,
      ...pageInfo,
    ]

    return allInfo.join('\n')
  }

  // 提交反馈
  const handleSubmit = async (values: { mgTitle: string; mgContent: string }) => {
    if (!userInfo) {
      message.error(t('feedback.loginRequired'))
      return
    }

    setSubmitting(true)

    try {
      // 格式化反馈内容
      const formattedContent = formatFeedbackContent(values.mgContent)

      // 构建动态标题：{应用名称}-{语言代码}（Feedback）
      const feedbackText = currentLanguage === 'zh' ? '意见反馈' : 'Feedback'
      const titleWithAppAndLang = `${currentAppName}-${currentLanguage}（${feedbackText}）`

      const feedbackData: FeedbackData = {
        projectId: getFeedbackProjectId(),
        mgTitle: titleWithAppAndLang, // 使用应用名称+语言后缀作为标题
        mgRealName: userInfo.realName || userInfo.userName || '', // 保持原有的用户信息
        mgTell: userInfo.mobile || '',
        mgEmail: userInfo.email || getDefaultEmail(), // 使用默认邮箱
        mgUnit: '',
        mgContent: formattedContent, // 使用格式化后的内容
        mgAccessoryList: uploadedImages, // 上传的图片列表
        mgIsPublic: 1,
        mgType: 0,
        mgSource: 'PC',
        mgUserid: 0,
        mgUsername: userInfo.userName || '',
        versionInfo: '1.0.0',
        moduleType: 'feedback',
        objectId: 0,
        objectTitle: titleWithAppAndLang,
        clientIp: ''
      }

      // 提交意见反馈（使用xai-api）
      const responseData = await xaiApi.submitFeedback(feedbackData)
      console.log('API响应数据:', responseData) // 调试日志
      
      // 根据用户反馈，接口返回 {"status": 200, "message": "成功", "data": null}
      if (responseData && responseData.status === 200) {
        message.success(t('feedback.submitSuccess'))
        form.resetFields()
        setUploadedImages([])
        onCancel()
      } else {
        throw new Error(responseData?.message || 'Submit failed')
      }
    } catch (error) {
      console.error('提交反馈失败:', error)
      message.error(t('feedback.submitError'))
    } finally {
      setSubmitting(false)
    }
  }

  const handleCancel = () => {
    form.resetFields()
    setUploadedImages([])
    onCancel()
  }

  return (
    <Modal
      title={t('navigation.feedback')}
      open={open}
      onCancel={handleCancel}
      footer={null}
      destroyOnClose
      width={680} // 增加PC端宽度
      centered
      destroyOnHidden
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        className="mt-4"
      >
        {/* 隐藏标题输入框，标题将自动生成为应用名称+语言后缀 */}
        <Form.Item
          name="mgTitle"
          style={{ display: 'none' }}
          initialValue="feedback" // 设置一个默认值以通过表单验证
        >
          <Input />
        </Form.Item>

        <Form.Item
          name="mgContent"
          label={t('feedback.contentLabel')}
          rules={[
            { required: true, message: t('feedback.contentRequired') },
            { min: 10, message: t('feedback.contentMinLength') },
            { max: 1000, message: t('feedback.contentMaxLength') }
          ]}
        >
          <TextArea
            placeholder={t('feedback.contentPlaceholder')}
            rows={6}
            maxLength={1000}
            showCount
          />
        </Form.Item>

        {/* 图片上传组件（使用拖拽上传） */}
        <Form.Item
          label={t('feedback.uploadImages')}
        >
          <div className="space-y-3">
            {/* 拖拽上传组件 */}
            <FileUpload
              ref={fileUploadRef}
              onFilesUploaded={handleImageUpload}
              allowedFileTypes={['png', 'jpg', 'jpeg', 'gif']}
              disabled={uploading || uploadedImages.length >= 3}
              isLoading={uploading}
              maxFiles={3}
              currentFileCount={uploadedImages.length}
            >
              <div 
                className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 hover:bg-blue-50 transition-all duration-200 cursor-pointer"
                onClick={() => {
                  // 点击整个区域触发文件选择
                  if (!uploading && uploadedImages.length < 3) {
                    fileUploadRef.current?.triggerFileSelect()
                  }
                }}
              >
                <div className="space-y-3">
                  <div className="text-5xl text-blue-400">📷</div>
                  <div className="text-lg font-medium text-gray-700">
                    {uploading ? t('feedback.uploading') : t('feedback.uploadImages')}
                  </div>
                  <div className="text-sm text-gray-500 leading-relaxed">
                    {t('feedback.uploadImagesTip')}
                  </div>
                  <div className="text-xs text-gray-400">
                    {t('feedback.dragOrClick')}
                  </div>
                </div>
              </div>
            </FileUpload>
            
            {/* 已上传的图片列表 */}
            {uploadedImages.length > 0 && (
              <div className="grid grid-cols-2 gap-3">
                {uploadedImages.map((img, index) => (
                  <div key={index} className="relative group">
                    <Image
                      src={img.url}
                      alt={`上传的图片 ${index + 1}`}
                      className="w-full h-24 object-cover rounded border"
                      style={{ objectFit: 'cover' }}
                    />
                    <Button
                      icon={<DeleteOutlined />}
                      size="small"
                      type="primary"
                      danger
                      className="absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity"
                      onClick={() => handleImageRemove(img.url)}
                    />
                  </div>
                ))}
              </div>
            )}
            
            {uploadedImages.length >= 3 && (
              <div className="text-sm text-gray-500">
                {t('feedback.maxImagesReached')}
              </div>
            )}
          </div>
        </Form.Item>

        <div className="flex justify-end gap-3 mt-6">
          <Button onClick={handleCancel}>
            {t('common.cancel')}
          </Button>
          <Button
            type="primary"
            htmlType="submit"
            loading={submitting}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {t('feedback.submit')}
          </Button>
        </div>
      </Form>
    </Modal>
  )
}

export default FeedbackModal
