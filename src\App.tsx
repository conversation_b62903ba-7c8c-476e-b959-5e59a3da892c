import { useState, useEffect } from 'react'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { HelmetProvider } from 'react-helmet-async'
import ChatDetail from './components/ChatDetail'
import Home from './components/Home';
import CaseExample from './components/CaseExample';
import I18nRouteWrapper from './components/I18nRouteWrapper';
import Login from './components/login/Login';
import SignUp from './components/login/SignUp';
import LoginManager from './components/LoginManager';
import Cookies from 'js-cookie';
import { XAiApi } from './api/src/xai-api';
import { DifyApi } from './api/src/dify-api';
import { useMount } from 'ahooks';
import { getEnvConfig } from './utils/envConfig';

function App() {
  const [user, setUser] = useState<string>('nologin')
	const [userInfo, setUserInfo]:any = useState(null)
  const [currentAppUuid, setCurrentAppUuid] = useState<string>('')


  // 从环境配置获取API基础地址
  const envConfig = getEnvConfig()
  const apiBase = envConfig.apiBase
  const xAiApi = new XAiApi({
    user: user,
    apiBase: apiBase,
    medxyToken: Cookies.get('medxyToken') || ''
  });
  const difyApi = new DifyApi({
    user: user,
    apiBase: apiBase,
    medxyToken: Cookies.get('medxyToken') || ''
  });

  useMount(() => {
		// 从cookie中获取userInfo
		const userInfoString = Cookies.get('userInfo')
		if (userInfoString) {
      const userInfos = JSON.parse(userInfoString)
			setUserInfo(userInfos)
      setUser(userInfos.userName)

			const medxyToken = Cookies.get('medxyToken')
			if (medxyToken) {
				return;
			}
      
      xAiApi.getAiWriteToken({
          userId: userInfos.userId,
          userName: userInfos.userName,
          realName: userInfos.realName,
          avatar: userInfos.avatar,
          plaintextUserId: userInfos.plaintextUserId,
          mobile: userInfos.mobile,
          email: userInfos.email
      }).then((data:any) => {
        if (data?.token) {
					const hostname = window.location.hostname
					Cookies.set("medxyToken", data.token);
          localStorage.setItem("hasuraToken", data.htoken);
          localStorage.setItem("openid", data.openid);
          localStorage.setItem("socialUserId", data.socialUserId);
          localStorage.setItem("socialType", data.socialType);
        } else {
          console.error("登录失败: 未返回 token");
        }
      })
		}
	})

	useEffect(() => {
		if (userInfo) {
			setUser(userInfo.userName)

      const medxyToken = Cookies.get('medxyToken')
			if (medxyToken) {
				return;
			}

			xAiApi.getAiWriteToken({
				userId: userInfo.userId,
				userName: userInfo.userName,
				realName: userInfo.realName,
				avatar: userInfo.avatar,
				plaintextUserId: userInfo.plaintextUserId,
				mobile: userInfo.mobile,
				email: userInfo.email
			}).then((data: any) => {
				if (data?.token) {
					const hostname = window.location.hostname
					Cookies.set("medxyToken", data.token );
					localStorage.setItem("hasuraToken", data.htoken);
					localStorage.setItem("openid", data.openid);
					localStorage.setItem("socialUserId", data.socialUserId);
					localStorage.setItem("socialType", data.socialType);
				} else {
					console.error("登录失败: 未返回 token");
				}
			})
		}
	}, [userInfo]) // 依赖 userInfo 和 appservice

	// 监听登录状态变化事件
	useEffect(() => {
		const handleUserInfoUpdate = (event: CustomEvent) => {
			console.log('App: 收到用户信息更新事件', event.detail)
			const userInfoString = Cookies.get('userInfo')
			if (userInfoString) {
				const userInfos = JSON.parse(userInfoString)
				setUserInfo(userInfos)
				setUser(userInfos.userName)
			}
		}

		window.addEventListener('userInfoUpdated', handleUserInfoUpdate as EventListener)

		return () => {
			window.removeEventListener('userInfoUpdated', handleUserInfoUpdate as EventListener)
		}
	}, [])


  return (
    <HelmetProvider>
      <Router>
        <I18nRouteWrapper>
          {/* 登录管理组件 - 处理无刷新登录 */}
          <LoginManager />
          <Routes>
          {/* 根路径重定向 */}
          <Route path="/" element={<Home
            difyApi={difyApi}
            xAiApi={xAiApi}
            currentAppUuid={currentAppUuid}
            setCurrentAppUuid={setCurrentAppUuid}
            user={user}
             />} />
          
          {/* 登录和注册路由 */}
          {!envConfig.isXAi && (
            <>
              <Route path="/:lang/login" element={<Login />} />
              <Route path="/:lang/login/:socialType" element={<Login />} />
              <Route path="/:lang/sign-up" element={<SignUp />} />
            </>
          )}

          {/* 国际化路由结构: /[lang]/[app-name]/[session-id?] */}
          {/* 新对话页面 - 使用特殊标识符 */}
          <Route path="/:lang/:appName/new" element={<ChatDetail />} />

          {/* 案例页面路由 - 使用 cases 复数形式避免冲突 */}
          <Route path="/:lang/cases/:caseId" element={<CaseExample
            currentAppUuid={currentAppUuid}
            xAiApi={xAiApi} />} />

          {/* 历史对话页面 - 有具体的conversationId */}
          <Route path="/:lang/:appName/:sessionId" element={<ChatDetail />} />

          {/* 应用首页 - 精确匹配，不带末尾斜杠 */}
          <Route path="/:lang/:appName" element={<Home
            difyApi={difyApi}
            xAiApi={xAiApi}
            currentAppUuid={currentAppUuid}
            setCurrentAppUuid={setCurrentAppUuid}
            user={user}
             />} />



          {/* 兼容旧路由 */}
          <Route path="/chat" element={<ChatDetail />} />
          <Route path="/chat/:appUuid" element={<ChatDetail />} />
          <Route path="/chat/:appUuid/:conversationId" element={<ChatDetail />} />
          </Routes>
        </I18nRouteWrapper>
      </Router>
    </HelmetProvider>
  );
}

export default App
