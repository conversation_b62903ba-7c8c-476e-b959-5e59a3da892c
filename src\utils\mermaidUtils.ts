/**
 * Mermaid 工具函数
 * 基于 Dify 的最佳实践
 */

/**
 * 清理 SVG 代码
 */
export function cleanUpSvgCode(svgCode: string): string {
  return svgCode.replace(/<br>/g, '<br/>');
}

/**
 * 预处理 Mermaid 代码，修复常见语法问题
 */
export function prepareMermaidCode(mermaidCode: string, style: 'classic' | 'handDrawn' = 'classic'): string {
  if (!mermaidCode || typeof mermaidCode !== 'string') {
    return '';
  }

  let code = mermaidCode.trim();

  // 安全性：清理 javascript: 协议防止 XSS 攻击
  code = code.replace(/(\bclick\s+\w+\s+")javascript:[^"]*(")/g, '$1#$2');

  // 便利性：基本的 BR 标签替换
  code = code.replace(/<br\s*\/?>/g, '\n');

  let finalCode = code;

  // 手绘风格需要特殊处理
  if (style === 'handDrawn') {
    finalCode = finalCode
      .replace(/style\s+[^\n]+/g, '')
      .replace(/linkStyle\s+[^\n]+/g, '')
      .replace(/^flowchart/, 'graph')
      .replace(/class="[^"]*"/g, '')
      .replace(/fill="[^"]*"/g, '')
      .replace(/stroke="[^"]*"/g, '');

    // 确保手绘风格图表始终以 graph 开头
    if (!finalCode.startsWith('graph') && !finalCode.startsWith('flowchart')) {
      finalCode = `graph TD\n${finalCode}`;
    }
  }

  return finalCode;
}

/**
 * 将 SVG 转换为 base64 字符串
 */
export function svgToBase64(svgGraph: string): Promise<string> {
  if (!svgGraph) {
    return Promise.resolve('');
  }

  try {
    // 确保 SVG 有正确的 XML 声明
    if (!svgGraph.includes('<?xml')) {
      svgGraph = `<?xml version="1.0" encoding="UTF-8"?>${svgGraph}`;
    }

    const blob = new Blob([new TextEncoder().encode(svgGraph)], { 
      type: 'image/svg+xml;charset=utf-8' 
    });
    
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => resolve(reader.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  } catch (error) {
    return Promise.resolve('');
  }
}

/**
 * 检查 Mermaid 代码是否完整和有效
 */
export function isMermaidCodeComplete(code: string): boolean {
  if (!code || code.trim().length === 0) {
    return false;
  }

  try {
    const trimmedCode = code.trim();

    // 甘特图特殊处理
    if (trimmedCode.startsWith('gantt')) {
      const lines = trimmedCode.split('\n').filter(line => line.trim().length > 0);
      return lines.length >= 3;
    }

    // 思维导图特殊处理
    if (trimmedCode.startsWith('mindmap')) {
      const lines = trimmedCode.split('\n').filter(line => line.trim().length > 0);
      return lines.length >= 2;
    }

    // 检查基本语法结构
    const hasValidStart = /^(graph|flowchart|sequenceDiagram|classDiagram|classDef|class|stateDiagram|gantt|pie|er|journey|requirementDiagram|mindmap)/.test(trimmedCode);

    if (!hasValidStart) {
      return false;
    }

    // 检查常见语法错误
    const hasNoSyntaxErrors = !trimmedCode.includes('undefined')
                           && !trimmedCode.includes('[object Object]')
                           && trimmedCode.split('\n').every(line =>
                             !(line.includes('-->') && !line.match(/\S+\s*-->\s*\S+/)));

    return hasValidStart && hasNoSyntaxErrors;
  } catch (error) {
    console.error('Mermaid code validation error:', error);
    return false;
  }
}

/**
 * DOM 元素等待助手，带重试机制
 */
export function waitForDOMElement(callback: () => Promise<any>, maxAttempts = 3, delay = 100): Promise<any> {
  return new Promise((resolve, reject) => {
    let attempts = 0;
    const tryRender = async () => {
      try {
        resolve(await callback());
      } catch (error) {
        attempts++;
        if (attempts < maxAttempts) {
          setTimeout(tryRender, delay);
        } else {
          reject(error);
        }
      }
    };
    tryRender();
  });
}

/**
 * 常用的 Mermaid 图表示例
 */
export const MERMAID_EXAMPLES = {
  flowchart: `graph TD
    A[开始] --> B{是否登录?}
    B -->|是| C[显示主页]
    B -->|否| D[显示登录页]
    C --> E[结束]
    D --> E`,
    
  sequence: `sequenceDiagram
    participant A as 用户
    participant B as 前端
    participant C as 后端
    participant D as 数据库
    
    A->>B: 登录请求
    B->>C: 验证用户
    C->>D: 查询用户信息
    D-->>C: 返回用户数据
    C-->>B: 验证结果
    B-->>A: 登录成功`,
    
  gantt: `gantt
    title 项目开发计划
    dateFormat  YYYY-MM-DD
    section 设计阶段
    需求分析           :done,    des1, 2024-01-01,2024-01-05
    UI设计            :active,  des2, 2024-01-06, 3d
    section 开发阶段
    前端开发          :         dev1, after des2, 5d
    后端开发          :         dev2, after des2, 5d`,
    
  pie: `pie title 用户设备分布
    "移动端" : 45
    "桌面端" : 35
    "平板" : 20`,
    
  mindmap: `mindmap
  root((项目管理))
    规划
      需求分析
      时间安排
      资源分配
    执行
      开发
      测试
      部署
    监控
      进度跟踪
      质量控制
      风险管理`,

  classDiagram: `classDiagram
    class User {
        +String name
        +String email
        +login()
        +logout()
    }
    class Admin {
        +manageUsers()
        +viewReports()
    }
    User <|-- Admin`,

  stateDiagram: `stateDiagram-v2
    [*] --> 待处理
    待处理 --> 处理中 : 开始处理
    处理中 --> 已完成 : 处理完成
    处理中 --> 已取消 : 取消处理
    已完成 --> [*]
    已取消 --> [*]`
};

/**
 * 验证 Mermaid 代码是否有效
 */
export function validateMermaidCode(code: string): boolean {
  if (!code || !code.trim()) {
    return false;
  }
  
  const trimmed = code.trim();
  const validStarters = [
    'graph', 'flowchart', 'sequenceDiagram', 'classDiagram', 'stateDiagram',
    'erDiagram', 'journey', 'gantt', 'pie', 'gitGraph', 'mindmap',
    'timeline', 'quadrantChart', 'xyChart', 'requirementDiagram'
  ];
  
  return validStarters.some(starter => trimmed.startsWith(starter));
}
