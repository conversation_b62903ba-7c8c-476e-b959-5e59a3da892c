import React, { useState, useEffect } from 'react'
import { useSimpleTranslation } from '../../i18n/simple-hooks'

/**
 * 加载消息组件 - 显示AI正在思考/回复的状态
 * 包含动画效果和国际化支持
 */
const LoadingMessage: React.FC = () => {
  const { t } = useSimpleTranslation()
  const [dots, setDots] = useState('')

  // 动态显示点点点的动画效果
  useEffect(() => {
    const interval = setInterval(() => {
      setDots(prev => {
        if (prev === '...') return ''
        return prev + '.'
      })
    }, 500)

    return () => clearInterval(interval)
  }, [])

  return (
    <div className="flex items-start space-x-4 mb-6">
      {/* AI头像 */}
      <div className="flex-shrink-0">
        <div className="w-8 h-8 rounded-full bg-gradient-to-br from-purple-500 to-blue-500 flex items-center justify-center shadow-lg">
          <div className="w-4 h-4 bg-white rounded-full animate-pulse"></div>
        </div>
      </div>

      {/* 加载消息内容 */}
      <div className="flex-1 max-w-4xl">
        <div className="bg-gray-50 rounded-2xl rounded-tl-sm p-4 shadow-sm border border-gray-100">
          <div className="flex items-center space-x-2">
            {/* 脉冲动画图标 */}
            <div className="flex space-x-1">
              <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
              <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
              <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
            </div>
            
            {/* 加载文本 */}
            <span className="text-gray-600 text-sm font-medium">
              {t('chat.loadingMessage')}{dots}
            </span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default LoadingMessage
