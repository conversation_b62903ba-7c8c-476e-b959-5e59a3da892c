import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// SEO验证规则
const seoRules = {
  title: {
    required: true,
    minLength: 10,
    maxLength: 60,
    message: '标题应该在10-60个字符之间'
  },
  description: {
    required: true,
    minLength: 50,
    maxLength: 160,
    message: '描述应该在50-160个字符之间'
  },
  keywords: {
    required: true,
    minCount: 3,
    maxCount: 10,
    message: '关键词应该在3-10个之间'
  },
  ogTitle: {
    required: true,
    message: 'Open Graph标题是必需的'
  },
  ogDescription: {
    required: true,
    message: 'Open Graph描述是必需的'
  },
  ogImage: {
    required: true,
    message: 'Open Graph图片是必需的'
  },
  canonical: {
    required: true,
    message: 'Canonical链接是必需的'
  },
  structuredData: {
    required: true,
    message: '结构化数据是必需的'
  }
}

// 解析HTML文件并提取SEO信息
function extractSEOInfo(htmlContent) {
  const seoInfo = {}
  
  // 提取标题
  const titleMatch = htmlContent.match(/<title[^>]*>(.*?)<\/title>/i)
  seoInfo.title = titleMatch ? titleMatch[1].trim() : null
  
  // 提取描述
  const descMatch = htmlContent.match(/<meta[^>]*name="description"[^>]*content="([^"]*)"[^>]*>/i)
  seoInfo.description = descMatch ? descMatch[1].trim() : null
  
  // 提取关键词
  const keywordsMatch = htmlContent.match(/<meta[^>]*name="keywords"[^>]*content="([^"]*)"[^>]*>/i)
  seoInfo.keywords = keywordsMatch ? keywordsMatch[1].trim().split(',').map(k => k.trim()) : []
  
  // 提取Open Graph标签
  const ogTitleMatch = htmlContent.match(/<meta[^>]*property="og:title"[^>]*content="([^"]*)"[^>]*>/i)
  seoInfo.ogTitle = ogTitleMatch ? ogTitleMatch[1].trim() : null
  
  const ogDescMatch = htmlContent.match(/<meta[^>]*property="og:description"[^>]*content="([^"]*)"[^>]*>/i)
  seoInfo.ogDescription = ogDescMatch ? ogDescMatch[1].trim() : null
  
  const ogImageMatch = htmlContent.match(/<meta[^>]*property="og:image"[^>]*content="([^"]*)"[^>]*>/i)
  seoInfo.ogImage = ogImageMatch ? ogImageMatch[1].trim() : null
  
  // 提取Canonical链接
  const canonicalMatch = htmlContent.match(/<link[^>]*rel="canonical"[^>]*href="([^"]*)"[^>]*>/i)
  seoInfo.canonical = canonicalMatch ? canonicalMatch[1].trim() : null
  
  // 检查结构化数据
  const structuredDataMatch = htmlContent.match(/<script[^>]*type="application\/ld\+json"[^>]*>(.*?)<\/script>/is)
  seoInfo.structuredData = structuredDataMatch ? JSON.parse(structuredDataMatch[1].trim()) : null
  
  // 提取Twitter Card标签
  const twitterCardMatch = htmlContent.match(/<meta[^>]*name="twitter:card"[^>]*content="([^"]*)"[^>]*>/i)
  seoInfo.twitterCard = twitterCardMatch ? twitterCardMatch[1].trim() : null
  
  return seoInfo
}

// 验证SEO信息
function validateSEO(seoInfo, filePath) {
  const results = {
    file: filePath,
    passed: 0,
    failed: 0,
    warnings: 0,
    issues: []
  }
  
  // 验证标题
  if (!seoInfo.title) {
    results.issues.push({ type: 'error', field: 'title', message: '缺少页面标题' })
    results.failed++
  } else {
    const titleLength = seoInfo.title.length
    if (titleLength < seoRules.title.minLength || titleLength > seoRules.title.maxLength) {
      results.issues.push({ 
        type: 'warning', 
        field: 'title', 
        message: `${seoRules.title.message}，当前长度: ${titleLength}` 
      })
      results.warnings++
    } else {
      results.passed++
    }
  }
  
  // 验证描述
  if (!seoInfo.description) {
    results.issues.push({ type: 'error', field: 'description', message: '缺少页面描述' })
    results.failed++
  } else {
    const descLength = seoInfo.description.length
    if (descLength < seoRules.description.minLength || descLength > seoRules.description.maxLength) {
      results.issues.push({ 
        type: 'warning', 
        field: 'description', 
        message: `${seoRules.description.message}，当前长度: ${descLength}` 
      })
      results.warnings++
    } else {
      results.passed++
    }
  }
  
  // 验证关键词
  if (!seoInfo.keywords || seoInfo.keywords.length === 0) {
    results.issues.push({ type: 'error', field: 'keywords', message: '缺少关键词' })
    results.failed++
  } else {
    const keywordCount = seoInfo.keywords.length
    if (keywordCount < seoRules.keywords.minCount || keywordCount > seoRules.keywords.maxCount) {
      results.issues.push({ 
        type: 'warning', 
        field: 'keywords', 
        message: `${seoRules.keywords.message}，当前数量: ${keywordCount}` 
      })
      results.warnings++
    } else {
      results.passed++
    }
  }
  
  // 验证Open Graph标签
  ['ogTitle', 'ogDescription', 'ogImage'].forEach(field => {
    if (!seoInfo[field]) {
      results.issues.push({ 
        type: 'error', 
        field, 
        message: seoRules[field].message 
      })
      results.failed++
    } else {
      results.passed++
    }
  })
  
  // 验证Canonical链接
  if (!seoInfo.canonical) {
    results.issues.push({ 
      type: 'error', 
      field: 'canonical', 
      message: seoRules.canonical.message 
    })
    results.failed++
  } else {
    results.passed++
  }
  
  // 验证结构化数据
  if (!seoInfo.structuredData) {
    results.issues.push({ 
      type: 'warning', 
      field: 'structuredData', 
      message: seoRules.structuredData.message 
    })
    results.warnings++
  } else {
    results.passed++
  }
  
  // 验证Twitter Card
  if (!seoInfo.twitterCard) {
    results.issues.push({ 
      type: 'warning', 
      field: 'twitterCard', 
      message: 'Twitter Card标签缺失' 
    })
    results.warnings++
  } else {
    results.passed++
  }
  
  return results
}

// 扫描dist目录中的HTML文件
function scanHTMLFiles(distDir) {
  const htmlFiles = []
  
  function scanDirectory(dir) {
    const items = fs.readdirSync(dir)
    
    for (const item of items) {
      const fullPath = path.join(dir, item)
      const stat = fs.statSync(fullPath)
      
      if (stat.isDirectory()) {
        scanDirectory(fullPath)
      } else if (item === 'index.html') {
        htmlFiles.push(fullPath)
      }
    }
  }
  
  scanDirectory(distDir)
  return htmlFiles
}

// 生成SEO报告
function generateSEOReport(results) {
  const totalFiles = results.length
  const totalPassed = results.reduce((sum, r) => sum + r.passed, 0)
  const totalFailed = results.reduce((sum, r) => sum + r.failed, 0)
  const totalWarnings = results.reduce((sum, r) => sum + r.warnings, 0)
  
  console.log('\n🔍 SEO验证报告')
  console.log('=' .repeat(50))
  console.log(`📊 总计: ${totalFiles} 个文件`)
  console.log(`✅ 通过: ${totalPassed} 项`)
  console.log(`❌ 失败: ${totalFailed} 项`)
  console.log(`⚠️  警告: ${totalWarnings} 项`)
  console.log('')
  
  results.forEach(result => {
    const relativePath = result.file.replace(process.cwd(), '.')
    console.log(`📄 ${relativePath}`)
    
    if (result.issues.length === 0) {
      console.log('  ✅ 所有SEO检查通过')
    } else {
      result.issues.forEach(issue => {
        const icon = issue.type === 'error' ? '❌' : '⚠️'
        console.log(`  ${icon} ${issue.field}: ${issue.message}`)
      })
    }
    console.log('')
  })
  
  // 生成评分
  const totalChecks = totalPassed + totalFailed + totalWarnings
  const score = totalChecks > 0 ? Math.round((totalPassed / totalChecks) * 100) : 0
  
  console.log(`🎯 SEO评分: ${score}/100`)
  
  if (score >= 90) {
    console.log('🎉 优秀！您的SEO配置非常完善')
  } else if (score >= 70) {
    console.log('👍 良好！还有一些改进空间')
  } else if (score >= 50) {
    console.log('⚠️  一般，建议优化SEO配置')
  } else {
    console.log('❌ 需要大幅改进SEO配置')
  }
  
  return { score, totalFiles, totalPassed, totalFailed, totalWarnings }
}

// 主函数
async function validateSEOFiles() {
  const distDir = path.resolve(__dirname, '../dist')
  
  if (!fs.existsSync(distDir)) {
    console.error('❌ dist目录不存在，请先运行构建命令')
    process.exit(1)
  }
  
  console.log('🔍 开始SEO验证...')
  
  const htmlFiles = scanHTMLFiles(distDir)
  
  if (htmlFiles.length === 0) {
    console.error('❌ 未找到HTML文件')
    process.exit(1)
  }
  
  const results = []
  
  for (const filePath of htmlFiles) {
    const htmlContent = fs.readFileSync(filePath, 'utf-8')
    const seoInfo = extractSEOInfo(htmlContent)
    const validation = validateSEO(seoInfo, filePath)
    results.push(validation)
  }
  
  const report = generateSEOReport(results)
  
  // 如果有严重错误，退出码为1
  if (report.totalFailed > 0) {
    process.exit(1)
  }
}

// 执行SEO验证
validateSEOFiles().catch(error => {
  console.error('❌ SEO验证失败:', error)
  process.exit(1)
})
