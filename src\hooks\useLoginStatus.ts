import { useState, useEffect, useCallback } from 'react'
import Cookies from 'js-cookie'

interface UserInfo {
  userId: string
  userName: string
  realName: string
  avatar: string
  plaintextUserId: string
  mobile: string
  email: string
}

/**
 * 登录状态管理 Hook
 * 提供统一的登录状态管理和事件监听
 */
export const useLoginStatus = () => {
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null)
  const [isLoggedIn, setIsLoggedIn] = useState(false)

  // 检查用户信息
  const checkUserInfo = useCallback(() => {
    const userInfoString = Cookies.get('userInfo')
    const medxyToken = Cookies.get('medxyToken')

    if (userInfoString && medxyToken) {
      try {
        const parsedInfo = JSON.parse(userInfoString)
        // 只有当用户信息真正发生变化时才更新状态
        setUserInfo(prevUserInfo => {
          if (!prevUserInfo || JSON.stringify(prevUserInfo) !== JSON.stringify(parsedInfo)) {
            console.log('用户信息已更新:', parsedInfo.userName)
            return parsedInfo
          }
          return prevUserInfo
        })
        setIsLoggedIn(true)
        return parsedInfo
      } catch (error) {
        console.error('解析用户信息失败:', error)
        setUserInfo(null)
        setIsLoggedIn(false)
      }
    } else {
      setUserInfo(prevUserInfo => {
        if (prevUserInfo !== null) {
          console.log('用户已退出登录')
          return null
        }
        return prevUserInfo
      })
      setIsLoggedIn(false)
    }
    return null
  }, [])

  // 强制刷新用户信息
  const refreshUserInfo = useCallback(() => {
    console.log('强制刷新用户信息')
    return checkUserInfo()
  }, [checkUserInfo])

  // 清除用户信息（用于退出登录）
  const clearUserInfo = useCallback(() => {
    console.log('清除用户信息')
    setUserInfo(null)
    setIsLoggedIn(false)
    // 触发用户信息清除事件
    window.dispatchEvent(new CustomEvent('userInfoCleared', {
      detail: { type: 'logout' }
    }))
  }, [])

  // 监听登录状态变化
  useEffect(() => {
    // 立即检查登录状态，确保初始化时就有正确的状态
    const initialUserInfo = checkUserInfo()
    if (initialUserInfo) {
      console.log('初始化时发现用户信息:', initialUserInfo.userName)
    }

    // 监听登录状态更新事件
    const handleUserInfoUpdate = (event: CustomEvent) => {
      console.log('useLoginStatus: 收到用户信息更新事件', event.detail)

      // 如果事件中包含用户信息，立即使用
      if (event.detail?.userInfo) {
        const userInfo = event.detail.userInfo
        console.log('立即更新用户信息:', userInfo.userName)
        setUserInfo(userInfo)
        setIsLoggedIn(true)
      } else {
        // 否则立即检查Cookie
        const result = checkUserInfo()
        if (result) {
          console.log('从Cookie立即更新用户信息:', result.userName)
        }
      }
    }

    // 监听登录状态变化事件
    const handleLoginStatusChange = (event: CustomEvent) => {
      console.log('useLoginStatus: 收到登录状态变化事件', event.detail)
      checkUserInfo()
    }

    // 监听用户信息清除事件
    const handleUserInfoClear = (event: CustomEvent) => {
      console.log('useLoginStatus: 收到用户信息清除事件', event.detail)
      setUserInfo(null)
      setIsLoggedIn(false)
    }

    // 添加事件监听器
    window.addEventListener('userInfoUpdated', handleUserInfoUpdate as EventListener)
    window.addEventListener('loginStatusChanged', handleLoginStatusChange as EventListener)
    window.addEventListener('userInfoCleared', handleUserInfoClear as EventListener)

    // 定期检查（作为备用机制，降低频率避免过度更新）
    const interval = setInterval(checkUserInfo, 10000) // 改为10秒检查一次

    return () => {
      window.removeEventListener('userInfoUpdated', handleUserInfoUpdate as EventListener)
      window.removeEventListener('loginStatusChanged', handleLoginStatusChange as EventListener)
      window.removeEventListener('userInfoCleared', handleUserInfoClear as EventListener)
      clearInterval(interval)
    }
  }, [checkUserInfo])

  return {
    userInfo,
    isLoggedIn,
    refreshUserInfo,
    checkUserInfo,
    clearUserInfo
  }
}

export default useLoginStatus
