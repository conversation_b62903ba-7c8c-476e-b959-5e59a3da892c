import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 预渲染的路由
const routes = [
  '/',
  '/zh/novax-base',
  '/zh/novax-pro', 
  '/zh/elavax-base',
  '/zh/elavax-pro',
  '/zh/datascore-base',
  '/en/novax-base',
  '/en/novax-pro',
  '/en/elavax-base', 
  '/en/elavax-pro',
  '/en/datascore-base'
]

// 基础HTML模板
const htmlTemplate = `<!DOCTYPE html>
<html lang="{{LANG}}">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>{{TITLE}}</title>
    <meta name="description" content="{{DESCRIPTION}}" />
    <meta property="og:title" content="{{TITLE}}" />
    <meta property="og:description" content="{{DESCRIPTION}}" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="{{URL}}" />
    <meta property="og:locale" content="{{LOCALE}}" />
    <script type="module" crossorigin src="/assets/index.js"></script>
    <link rel="stylesheet" crossorigin href="/assets/index.css">
  </head>
  <body>
    <div id="root">{{PRERENDERED_CONTENT}}</div>
    <script>
      // 标记为预渲染页面，避免hydration不匹配
      window.__PRERENDERED__ = true;
    </script>
  </body>
</html>`

// 真实的服务端渲染函数
async function renderReactComponentSSR(route) {
  try {
    console.log(`开始SSR渲染: ${route}`)
    
    // 设置Node.js环境变量
    process.env.NODE_ENV = 'production'
    
    // 动态导入React和相关依赖
    const React = (await import('react')).default
    const { renderToString } = await import('react-dom/server')
    const { HelmetProvider } = await import('react-helmet-async')

    // 使用MemoryRouter代替StaticRouter
    const { MemoryRouter } = await import('react-router-dom')
    
    // 模拟浏览器环境
    global.window = {
      location: { pathname: route, search: '', hash: '' },
      navigator: { userAgent: 'SSR' },
      document: { createElement: () => ({}) },
      localStorage: { getItem: () => null, setItem: () => {} },
      sessionStorage: { getItem: () => null, setItem: () => {} }
    }
    global.document = global.window.document
    global.navigator = global.window.navigator
    global.localStorage = global.window.localStorage
    global.sessionStorage = global.window.sessionStorage
    
    // 创建模拟的API实例
    const mockXAiApi = {
      getAppByConfigKey: async () => [],
      getConfigByKeyFromCache: async () => ({}),
    }
    
    const mockDifyApi = {
      options: { user: 'ssr-user', apiBase: '/dev-api', medxyToken: '', appId: 'ssr-app' }
    }
    
    // 创建模拟的Home组件props
    const homeProps = {
      difyApi: mockDifyApi,
      xAiApi: mockXAiApi,
      currentAppUuid: '',
      setCurrentAppUuid: () => {},
      user: 'ssr-user',
      serverData: {
        appList: await getMockAppList(),
        currentApp: getCurrentAppFromRoute(route),
        caseExamples: [],
        suggestedQuestions: {},
        appParameters: null,
        subscriptionStatus: null,
        allSubscriptions: {},
        conversationList: { data: [], has_more: false },
        userInfo: null,
        lang: route.startsWith('/en') ? 'en' : 'zh',
        appName: getAppNameFromRoute(route)
      }
    }
    
    // 动态导入Home组件
    const { default: Home } = await import('../src/components/Home.tsx')
    
    // 创建React元素
    const helmetContext = {}
    const app = React.createElement(
      HelmetProvider,
      { context: helmetContext },
      React.createElement(
        MemoryRouter,
        { initialEntries: [route] },
        React.createElement(Home, homeProps)
      )
    )
    
    // 渲染为HTML字符串
    const html = renderToString(app)
    
    console.log(`SSR渲染成功: ${route}, HTML长度: ${html.length}`)
    return html
    
  } catch (error) {
    console.error(`SSR渲染失败 (${route}):`, error)
    // 返回错误状态的HTML
    return `
      <div style="display: flex; justify-content: center; align-items: center; height: 100vh; font-family: system-ui, -apple-system, sans-serif;">
        <div style="text-align: center; color: #ef4444;">
          <div style="font-size: 24px; margin-bottom: 16px;">⚠️</div>
          <div style="font-size: 18px;">SSR渲染失败</div>
          <div style="font-size: 14px; margin-top: 8px; color: #666;">正在加载客户端版本...</div>
        </div>
      </div>
    `
  }
}

// 获取模拟应用列表
async function getMockAppList() {
  return [
    {
      id: 1,
      appUuid: 'novax-base-uuid',
      appLang: 'zh',
      appStatus: 'active',
      appName: 'NovaX AI',
      appNameEn: 'novax-base',
      appType: 'chat',
      appDescription: '您的灵感引擎，构建前瞻性研究蓝图',
      appIcon: '/icons/novax-icon.png',
      appUser: null,
      feeTypes: [],
      isInternalUser: 0,
      dAppUuid: 'novax-dapp-uuid',
      directoryMd: ''
    },
    {
      id: 2,
      appUuid: 'elavax-pro-uuid',
      appLang: 'zh',
      appStatus: 'active',
      appName: 'ElavaX Pro',
      appNameEn: 'elavax-pro',
      appType: 'chat',
      appDescription: '精准剖析，深度洞察，赋能您的卓越创见',
      appIcon: '/icons/elavax-icon.png',
      appUser: null,
      feeTypes: [],
      isInternalUser: 0,
      dAppUuid: 'elavax-pro-dapp-uuid',
      directoryMd: ''
    }
    // 可以添加更多应用...
  ]
}

// 从路由获取当前应用
function getCurrentAppFromRoute(route) {
  const appName = getAppNameFromRoute(route)
  const appList = [
    { appNameEn: 'novax-base', appName: 'NovaX AI', appUuid: 'novax-base-uuid' },
    { appNameEn: 'elavax-pro', appName: 'ElavaX Pro', appUuid: 'elavax-pro-uuid' }
  ]
  return appList.find(app => app.appNameEn === appName) || appList[0]
}

// 从路由获取应用名称
function getAppNameFromRoute(route) {
  const segments = route.split('/').filter(Boolean)
  return segments.length >= 2 ? segments[1] : 'novax-base'
}

// 根据路由生成页面信息
function getPageInfo(route) {
  const segments = route.split('/').filter(Boolean)
  const lang = segments[0] || 'zh'
  const appName = segments[1] || 'novax-base'
  
  const appNames = {
    'novax-base': { zh: 'NovaX AI', en: 'NovaX AI' },
    'elavax-pro': { zh: 'ElavaX Pro', en: 'ElavaX Pro' }
  }
  
  const descriptions = {
    'novax-base': { 
      zh: '您的灵感引擎，构建前瞻性研究蓝图', 
      en: 'Your inspiration engine for building forward-looking research blueprints' 
    },
    'elavax-pro': { 
      zh: '精准剖析，深度洞察，赋能您的卓越创见', 
      en: 'Precise analysis, deep insights, empowering your excellent ideas' 
    }
  }
  
  const title = appNames[appName]?.[lang] || 'AI Assistant'
  const description = descriptions[appName]?.[lang] || 'AI Assistant for Research'
  
  return { title, description, lang, appName }
}

// 生成预渲染文件
async function generateSSRPrerenderedFiles() {
  const distDir = path.resolve(__dirname, '../dist')
  
  // 确保dist目录存在
  if (!fs.existsSync(distDir)) {
    fs.mkdirSync(distDir, { recursive: true })
  }
  
  for (const route of routes) {
    const { title, description, lang } = getPageInfo(route)
    
    // 真实的SSR渲染
    const prerenderedContent = await renderReactComponentSSR(route)
    
    // 生成HTML内容
    const html = htmlTemplate
      .replace(/{{LANG}}/g, lang)
      .replace(/{{TITLE}}/g, title)
      .replace(/{{DESCRIPTION}}/g, description)
      .replace(/{{URL}}/g, `https://ai.medsci.cn${route}`)
      .replace(/{{LOCALE}}/g, lang === 'zh' ? 'zh_CN' : 'en_US')
      .replace(/{{PRERENDERED_CONTENT}}/g, prerenderedContent)
    
    // 确定文件路径
    let filePath
    if (route === '/') {
      filePath = path.join(distDir, 'index.html')
    } else {
      const routeDir = path.join(distDir, route)
      fs.mkdirSync(routeDir, { recursive: true })
      filePath = path.join(routeDir, 'index.html')
    }
    
    // 写入文件
    fs.writeFileSync(filePath, html)
    console.log(`Generated SSR: ${filePath}`)
  }
  
  console.log(`SSR预渲染完成！生成了 ${routes.length} 个页面`)
}

// 执行SSR预渲染
generateSSRPrerenderedFiles().catch(error => {
  console.error('SSR预渲染失败:', error)
  process.exit(1)
})
