import React, { useEffect } from 'react'
import { useParams, useNavigate, useLocation } from 'react-router-dom'
import { setLanguage } from '../i18n/simple'
import { getDefaultRoute, getDefaultLanguage, getDefaultAppName } from '../utils/envConfig'

interface I18nRouteWrapperProps {
  children: React.ReactNode
}

/**
 * 简化的国际化路由包装组件
 * 处理路由重定向和语言同步
 */
const I18nRouteWrapper: React.FC<I18nRouteWrapperProps> = ({ children }) => {
  const navigate = useNavigate()
  const location = useLocation()

  useEffect(() => {
    const currentPath = location.pathname
    const segments = currentPath.split('/').filter(Boolean)

    // 获取环境相关的默认值
    const defaultRoute = getDefaultRoute()
    const defaultLanguage = getDefaultLanguage()
    const defaultAppName = getDefaultAppName()

    // 如果是根路径，重定向到默认路由
    if (currentPath === '/') {
      navigate(defaultRoute, { replace: true })
      return
    }

    // 如果路径不包含语言代码，添加默认语言
    if (segments.length > 0 && segments[0] !== 'zh' && segments[0] !== 'en') {
      navigate(`/${defaultLanguage}${currentPath}`, { replace: true })
      return
    }

    // 如果路径只有语言代码，重定向到该语言的默认应用
    if (segments.length === 1 && (segments[0] === 'zh' || segments[0] === 'en')) {
      navigate(`${currentPath}/${defaultAppName}`, { replace: true })
      return
    }

    // 同步URL中的语言到全局状态
    if (segments.length > 0 && (segments[0] === 'zh' || segments[0] === 'en')) {
      setLanguage(segments[0] as 'zh' | 'en')
    }
  }, [location.pathname, navigate])

  return <>{children}</>
}

export default I18nRouteWrapper
